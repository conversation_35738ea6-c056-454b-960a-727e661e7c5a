# Interactive Visualization Guide for SWOT Analysis Platform

## Overview

This guide describes the interactive filtering and time-period selection features that have been added to the SWOT Analysis Platform's visualization module. These enhancements enable more dynamic data exploration and analysis, allowing users to:

- Filter visualizations by subject, performance level, time period, and SWOT category
- Select and compare different time periods
- Add annotations to charts
- Drill down into detailed views
- Export visualizations in various formats

## Components

The interactive visualization system consists of three main components:

1. **Interactive Filters** (`interactive_filters.py`) - Core filter classes for filtering data
2. **Interactive Visualization** (`interactive_visualization.py`) - Extended visualization module with filtering capabilities
3. **Interactive Demo** (`interactive_demo.py`) - Demonstration script for showing the new features

## 1. Filter Types

### Subject Filters

Subject filters allow you to focus on specific academic subjects. For example, you can:

- Filter to show only STEM subjects (Math, Science)
- Filter to show only Humanities subjects (English, History)
- Filter to show any custom selection of subjects

```python
# Example: Filter to show only Math and Science
subject_filter = FilterSet("Subjects")
subject_filter.add_filter(SubjectFilter("Mathematics"))
subject_filter.add_filter(SubjectFilter("Science"))
filtered_data = subject_filter.apply_all(student_data)
```

### Performance Filters

Performance filters let you focus on subjects based on score thresholds. For example:

- Filter to show only subjects with scores above 85% (strengths)
- Filter to show only subjects with scores below 70% (areas needing improvement)
- Create custom threshold ranges

```python
# Example: Filter to show only high-performing subjects
performance_filter = FilterSet("Performance")
performance_filter.add_filter(PerformanceFilter(threshold=85, comparison="above"))
filtered_data = performance_filter.apply_all(student_data)
```

### Time Period Filters

Time filters allow you to focus on specific academic periods. For example:

- Filter to show data for a specific quarter
- Filter to show data for a specific academic year
- Compare multiple time periods

```python
# Example: Filter to show only Q1 2024-2025 data
time_filter = FilterSet("Time")
time_filter.add_filter(TimeFilter(academic_year="2024-2025", quarter=1))
filtered_data = time_filter.apply_all(student_data)
```

### SWOT Category Filters

Category filters let you focus on specific aspects of the SWOT analysis. For example:

- Filter to show only Strengths and Weaknesses
- Filter to show only Opportunities and Threats
- Show any combination of SWOT categories

```python
# Example: Filter to show only Strengths and Opportunities
category_filter = FilterSet("SWOT")
category_filter.add_filter(CategoryFilter("strengths"))
category_filter.add_filter(CategoryFilter("opportunities"))
filtered_data = category_filter.apply_all(swot_analysis)
```

## 2. Time Period Selection

The `TimePeriodSelector` component provides flexible options for selecting time periods:

### Single Period Selection

```python
# Select a specific quarter
time_selector = TimePeriodSelector()
time_selector.set_available_periods(
    academic_years=["2023-2024", "2024-2025"],
    quarters=[1, 2, 3, 4]
)
time_selector.select_period(academic_year="2024-2025", quarter=1)
```

### Period Range Selection

```python
# Select a range of quarters
time_selector.select_range(
    start_year="2023-2024", start_quarter=3,
    end_year="2024-2025", end_quarter=1
)
```

### Relative Period Selection

```python
# Select the last 3 quarters
time_selector.select_last_n_quarters(3)
```

## 3. Interactive Visualizations

The enhanced visualization module provides interactive versions of all the standard charts:

### Academic Radar Chart

The interactive academic radar chart allows filtering by subject and performance level:

```python
visualizer = InteractiveSWOTVisualizer()
fig, path = visualizer.generate_academic_radar_interactive(
    student_id="STU12345",
    filter_subjects=["Mathematics", "Science", "English"],
    min_score=80
)
```

### GPA Trend Chart

The interactive GPA trend chart allows selection of different time periods:

```python
fig, path = visualizer.generate_academic_trend_interactive(
    student_id="STU12345",
    academic_years=["2023-2024", "2023-2024", "2024-2025"],
    quarters=[3, 4, 1],
    with_class_avg=True
)
```

### Attendance Heatmap

The interactive attendance heatmap allows filtering by month and quarter:

```python
fig, path = visualizer.generate_attendance_heatmap_interactive(
    student_id="STU12345",
    academic_year="2024-2025",
    quarter=1
)
```

### SWOT Quadrant Chart

The interactive SWOT quadrant chart allows filtering by category:

```python
fig, path = visualizer.generate_swot_quadrant_interactive(
    swot_analysis=swot_data,
    filter_categories=["strengths", "weaknesses"]
)
```

## 4. Annotations and Drill-down

### Adding Annotations

Annotations can be added to any chart to highlight important insights:

```python
visualizer.annotations.add_annotation(
    chart_id="academic_radar_STU12345",
    x="Mathematics",
    y=87,
    text="Strong improvement since last quarter",
    author="Teacher"
)
```

### Clickable Elements

Elements in charts can be made clickable to enable drill-down functionality:

```python
# Register a click handler for a chart element
def on_math_click(element_id, x, y):
    print("Showing detailed math performance...")

visualizer.element_manager.register_click_handler(
    chart_id="academic_radar_STU12345",
    element_id="math_point",
    handler=on_math_click
)
```

## 5. Export Features

Visualizations can be exported in various formats:

```python
# Export as PNG
png_path = visualizer.export_visualization(
    fig=fig,
    format='png',
    output_path="charts/academic_radar.png"
)

# Export as PDF
pdf_path = visualizer.export_visualization(
    fig=fig,
    format='pdf',
    output_path="charts/academic_radar.pdf"
)

# Export as SVG
svg_path = visualizer.export_visualization(
    fig=fig,
    format='svg',
    output_path="charts/academic_radar.svg"
)

# Export as base64 string (for embedding in web applications)
base64_string = visualizer.export_visualization(
    fig=fig,
    format='base64'
)
```

## Implementation Details

### Filter Architecture

Each filter type inherits from a common `Filter` base class, which defines:

- `apply()` method for applying the filter to data
- `toggle()` method for enabling/disabling the filter
- Management of filter state (active/inactive)

Filters are grouped into `FilterSet` collections, which can be applied as a unit.

### Interactive Visualization Architecture

The `InteractiveSWOTVisualizer` class extends the base `SWOTVisualizer` with:

- Methods for applying filters to data before visualization
- Interactive versions of all chart generation methods
- Support for annotations and clickable elements
- Export capabilities for various formats

## Integration with SWOT Analysis Platform

The interactive visualization features are designed to integrate seamlessly with the existing SWOT analysis platform:

1. The filter classes can be applied to any data structure
2. The interactive visualizer extends the base visualizer class
3. The time period selector works with the existing data model
4. The annotation and export features are compatible with all chart types

## Future Enhancements

Planned enhancements for the interactive visualization system include:

1. **Web Interface Integration** - Connect the filter controls to a web UI
2. **Real-time Filtering** - Enable real-time updates as filters are changed
3. **Filter Presets** - Allow saving and loading of filter combinations
4. **Comparative Filtering** - Enhanced filtering for comparing multiple students
5. **Custom Filter Creation** - User-defined custom filters
6. **Dashboard Layouts** - Configurable dashboard layouts with multiple charts

## Conclusion

The interactive filtering and time-period selection features significantly enhance the SWOT analysis platform's visualization capabilities. These enhancements provide teachers and parents with powerful tools for exploring student data, identifying patterns, and gaining actionable insights.

By making the visualizations more interactive and flexible, the platform enables more personalized analysis of student strengths, weaknesses, opportunities, and threats.
# Deployment Instructions

This document provides comprehensive instructions for deploying the SWOT Analysis Platform in various environments, including system requirements, installation steps, configuration options, and maintenance procedures.

## Deployment Overview

The SWOT Analysis Platform can be deployed in several ways:

1. **Development Deployment**: Local deployment for development and testing
2. **Standalone Server Deployment**: Direct installation on a server
3. **Containerized Deployment**: Using Docker and Docker Compose
4. **Cloud Deployment**: Deployment to cloud platforms (AWS, Azure, GCP)

## System Requirements

### Hardware Requirements

#### Minimum Requirements (Testing/Development)
- **CPU**: 2+ cores
- **RAM**: 8 GB
- **Storage**: 10 GB

#### Recommended Requirements (Production)
- **CPU**: 4+ cores
- **RAM**: 16 GB
- **Storage**: 100+ GB (depends on number of students and data retention policy)

### Software Requirements

#### Operating System
- **Linux**: Ubuntu 20.04 LTS or later (recommended)
- **macOS**: Catalina (10.15) or later
- **Windows**: Windows 10/11 or Windows Server 2019+ (with WSL 2 for optimal performance)

#### Software Dependencies
- **Python**: 3.8 or higher
- **Database**: SQLite 3.30+ (development), MySQL 8.0+ or PostgreSQL 12+ (production)
- **Web Server**: Nginx (recommended) or Apache
- **Process Manager**: Gunicorn, uWSGI, or Supervisor

## Deployment Methods

### Development Deployment

For local development and testing:

1. **Clone the Repository**
```bash
git clone https://github.com/your-organization/swot-analysis-platform.git
cd swot-analysis-platform
```

2. **Create Virtual Environment**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install Dependencies**
```bash
pip install -r requirements.txt
```

4. **Initialize Database**
```bash
python scripts/initialize_db.py
```

5. **Generate Sample Data (Optional)**
```bash
python scripts/generate_sample_data.py
```

6. **Run Development Server**
```bash
python app.py --debug
```

The application will be available at `http://localhost:5000`.

### Standalone Server Deployment

For deployment on a dedicated server:

1. **Install System Dependencies**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y python3 python3-pip python3-venv nginx

# Red Hat/CentOS
sudo yum update
sudo yum install -y python39 python39-pip nginx
```

2. **Create Application User (Optional)**
```bash
sudo useradd -m -s /bin/bash swotapp
sudo su - swotapp
```

3. **Clone the Repository**
```bash
git clone https://github.com/your-organization/swot-analysis-platform.git
cd swot-analysis-platform
```

4. **Create Virtual Environment**
```bash
python3 -m venv venv
source venv/bin/activate
```

5. **Install Dependencies**
```bash
pip install --no-cache-dir -r requirements.txt
pip install gunicorn  # Process manager
```

6. **Configure Environment**
```bash
cp .env.example .env
# Edit .env with production settings
nano .env
```

7. **Initialize Database**
```bash
# For SQLite
python scripts/initialize_db.py

# For MySQL/PostgreSQL
# (Configure DB_CONNECTION in .env first)
python scripts/initialize_db.py --production
```

8. **Configure Gunicorn**
Create a service file at `/etc/systemd/system/swot-platform.service`:
```ini
[Unit]
Description=SWOT Analysis Platform
After=network.target

[Service]
User=swotapp
Group=www-data
WorkingDirectory=/home/<USER>/swot-analysis-platform
Environment="PATH=/home/<USER>/swot-analysis-platform/venv/bin"
ExecStart=/home/<USER>/swot-analysis-platform/venv/bin/gunicorn --workers 4 --bind 127.0.0.1:8000 wsgi:app
Restart=always

[Install]
WantedBy=multi-user.target
```

9. **Configure Nginx**
Create a site configuration at `/etc/nginx/sites-available/swot-platform`:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static/ {
        alias /home/<USER>/swot-analysis-platform/static/;
    }
}
```

10. **Enable and Start Services**
```bash
sudo ln -s /etc/nginx/sites-available/swot-platform /etc/nginx/sites-enabled/
sudo systemctl daemon-reload
sudo systemctl start swot-platform
sudo systemctl enable swot-platform
sudo systemctl restart nginx
```

11. **Configure SSL (Recommended)**
```bash
sudo apt install -y certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### Containerized Deployment (Docker)

For deployment using Docker:

1. **Install Docker and Docker Compose**
Follow the [official Docker installation guide](https://docs.docker.com/get-docker/) for your platform.

2. **Clone the Repository**
```bash
git clone https://github.com/your-organization/swot-analysis-platform.git
cd swot-analysis-platform
```

3. **Configure Environment**
```bash
cp .env.example .env
# Edit .env with production settings
nano .env
```

4. **Build and Start the Containers**
```bash
docker-compose up -d
```

This will start the following containers:
- Web application (Python/Flask)
- Database (MySQL/PostgreSQL)
- Web server (Nginx)
- Redis (for caching)

5. **Initialize the Database**
```bash
docker-compose exec app python scripts/initialize_db.py --production
```

The application will be available at `http://localhost:80` (or the port configured in docker-compose.yml).

### Cloud Deployment

#### AWS Deployment

1. **Create an EC2 Instance**
   - Amazon Linux 2 or Ubuntu Server
   - t3.medium or larger for production
   
2. **Configure Security Groups**
   - Allow HTTP (80) and HTTPS (443)
   - Restrict SSH (22) to known IPs
   
3. **Follow the Standalone Server Deployment steps above**

4. **Alternative: Use AWS Elastic Beanstalk**
   - Create a `Procfile` in the project root:
     ```
     web: gunicorn --workers=4 --bind=0.0.0.0:$PORT wsgi:app
     ```
   - Use the AWS EB CLI:
     ```bash
     pip install awsebcli
     eb init
     eb create production
     ```

#### Azure Deployment

1. **Use Azure App Service**
   - Create a `requirements.txt` file
   - Create a `web.config` file for Python configuration
   - Deploy using Azure CLI:
     ```bash
     az webapp up --sku B1 --name swot-platform
     ```

2. **Alternative: Azure Container Instances**
   - Build your Docker image
   - Push to Azure Container Registry
   - Deploy to ACI:
     ```bash
     az container create --resource-group myRG --name swot-platform --image myacr.azurecr.io/swot-platform:latest --dns-name-label swot-platform --ports 80
     ```

#### Google Cloud Platform

1. **Use Google App Engine**
   - Create an `app.yaml` file:
     ```yaml
     runtime: python39
     
     handlers:
     - url: /static
       static_dir: static
     
     - url: /.*
       script: auto
     ```
   - Deploy using gcloud:
     ```bash
     gcloud app deploy
     ```

2. **Alternative: Google Cloud Run**
   - Build your Docker image
   - Push to Google Container Registry
   - Deploy to Cloud Run:
     ```bash
     gcloud run deploy swot-platform --image gcr.io/your-project/swot-platform --platform managed
     ```

## Configuration Options

The platform is configured through environment variables which can be set directly or through a `.env` file.

### Core Configuration

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `ENV` | Environment mode | development | production |
| `DEBUG` | Enable debug mode | False | False |
| `SECRET_KEY` | Secret key for security | | LongRandomString |
| `ALLOWED_HOSTS` | Comma-separated hosts | localhost | app.example.com |

### Database Configuration

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `DB_CONNECTION` | Database connection | sqlite:///swot.db | mysql://user:pass@host/db |
| `DB_POOL_SIZE` | Connection pool size | 5 | 10 |
| `DB_POOL_RECYCLE` | Connection recycle (s) | 300 | 600 |

### Caching Configuration

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `CACHE_TYPE` | Cache type | simple | redis |
| `CACHE_REDIS_URL` | Redis URL | | redis://localhost:6379/0 |
| `CACHE_DEFAULT_TIMEOUT` | Cache TTL (s) | 300 | 600 |

### Security Configuration

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `JWT_SECRET_KEY` | JWT signing key | SECRET_KEY | DifferentLongRandomString |
| `JWT_ACCESS_TOKEN_EXPIRES` | Token expiry (s) | 3600 | 1800 |
| `CORS_ORIGINS` | Allowed CORS origins | | https://app.example.com |

### Logging Configuration

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `LOG_LEVEL` | Logging level | INFO | WARNING |
| `LOG_FORMAT` | Log format | default | json |
| `LOG_FILE` | Log file path | | /var/log/swot-platform.log |

### Advanced Configuration

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `WORKERS` | Gunicorn workers | 4 | 8 |
| `TIMEOUT` | Request timeout (s) | 30 | 60 |
| `MAX_UPLOAD_SIZE` | Max upload size (MB) | 5 | 10 |

## Database Setup

### MySQL Setup

1. **Create Database and User**
```sql
CREATE DATABASE swot_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'swotuser'@'localhost' IDENTIFIED BY 'securepassword';
GRANT ALL PRIVILEGES ON swot_platform.* TO 'swotuser'@'localhost';
FLUSH PRIVILEGES;
```

2. **Configure Environment**
```
DB_CONNECTION=mysql://swotuser:securepassword@localhost/swot_platform
```

### PostgreSQL Setup

1. **Create Database and User**
```sql
CREATE USER swotuser WITH PASSWORD 'securepassword';
CREATE DATABASE swot_platform OWNER swotuser;
```

2. **Configure Environment**
```
DB_CONNECTION=postgresql://swotuser:securepassword@localhost/swot_platform
```

### Database Initialization

Run the database initialization script:
```bash
python scripts/initialize_db.py --production
```

## Security Hardening

### Web Server Security

Add the following to your Nginx configuration:
```nginx
# Security headers
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Frame-Options "SAMEORIGIN" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:;" always;

# TLS configuration
ssl_protocols TLSv1.2 TLSv1.3;
ssl_prefer_server_ciphers on;
ssl_ciphers 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
ssl_session_timeout 1d;
ssl_session_cache shared:SSL:10m;
ssl_session_tickets off;
```

### File Permissions

Set appropriate file permissions:
```bash
# Application files
find /home/<USER>/swot-analysis-platform -type f -exec chmod 644 {} \;
find /home/<USER>/swot-analysis-platform -type d -exec chmod 755 {} \;

# Executable scripts
chmod 755 /home/<USER>/swot-analysis-platform/scripts/*.py

# Environment file
chmod 600 /home/<USER>/swot-analysis-platform/.env

# Data directory
chmod 750 /home/<USER>/swot-analysis-platform/data
```

### Firewall Configuration

Configure the firewall to allow only necessary traffic:
```bash
# Ubuntu (ufw)
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 22/tcp  # SSH access
sudo ufw enable

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --reload
```

## Monitoring and Maintenance

### Logging

Configure logging by setting the appropriate environment variables:
```
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=/var/log/swot-platform.log
```

For log rotation, create a configuration file at `/etc/logrotate.d/swot-platform`:
```
/var/log/swot-platform.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 swotapp swotapp
    sharedscripts
    postrotate
        systemctl reload swot-platform >/dev/null 2>&1 || true
    endscript
}
```

### Health Checks

Implement a health check endpoint to monitor system status:
```python
@app.route('/health')
def health_check():
    status = {
        'status': 'ok',
        'database': check_database_connection(),
        'cache': check_cache_connection(),
        'version': '1.0.0',
        'timestamp': datetime.utcnow().isoformat()
    }
    return jsonify(status)
```

Use external monitoring tools (e.g., Prometheus, Nagios) to periodically check this endpoint.

### Backup Strategy

#### Database Backup

Create a backup script at `/home/<USER>/scripts/backup_db.sh`:
```bash
#!/bin/bash
BACKUP_DIR="/home/<USER>/backups"
TIMESTAMP=$(date +"%Y%m%d%H%M%S")
BACKUP_FILE="$BACKUP_DIR/swot_db_$TIMESTAMP.sql"

# MySQL backup
mysqldump -u swotuser -p'securepassword' swot_platform > "$BACKUP_FILE"

# PostgreSQL backup
# pg_dump -U swotuser -d swot_platform -f "$BACKUP_FILE"

# Compress backup
gzip "$BACKUP_FILE"

# Clean old backups (keep last 14 days)
find "$BACKUP_DIR" -name "swot_db_*.sql.gz" -mtime +14 -delete
```

Make it executable and schedule with cron:
```bash
chmod +x /home/<USER>/scripts/backup_db.sh
crontab -e
```

Add the following line to run daily at 2 AM:
```
0 2 * * * /home/<USER>/scripts/backup_db.sh
```

#### File Backup

For file backups (uploads, generated reports, etc.):
```bash
#!/bin/bash
BACKUP_DIR="/home/<USER>/backups"
TIMESTAMP=$(date +"%Y%m%d%H%M%S")
BACKUP_FILE="$BACKUP_DIR/swot_files_$TIMESTAMP.tar.gz"

# Backup data files
tar -czf "$BACKUP_FILE" -C /home/<USER>/swot-analysis-platform/data .

# Clean old backups (keep last 14 days)
find "$BACKUP_DIR" -name "swot_files_*.tar.gz" -mtime +14 -delete
```

### Updating the Application

1. **Create a Maintenance Script**
Create an update script at `/home/<USER>/scripts/update_app.sh`:
```bash
#!/bin/bash
set -e

# Change to application directory
cd /home/<USER>/swot-analysis-platform

# Activate virtual environment
source venv/bin/activate

# Pull latest changes
git pull

# Install/update dependencies
pip install --no-cache-dir -r requirements.txt

# Run database migrations
python scripts/run_migrations.py

# Restart the service
sudo systemctl restart swot-platform

echo "Update completed successfully"
```

2. **Perform the Update**
```bash
chmod +x /home/<USER>/scripts/update_app.sh
./home/<USER>/scripts/update_app.sh
```

## Scaling Considerations

### Horizontal Scaling

For increased load handling:

1. **Load Balancer Setup**
   - Set up Nginx or HAProxy as a load balancer
   - Configure multiple application servers behind the load balancer
   - Ensure session persistence or stateless application design

2. **Database Scaling**
   - Consider read replicas for read-heavy workloads
   - Implement connection pooling
   - Shard data for very large deployments

### Vertical Scaling

For simpler deployments:

1. **Increase Resources**
   - Add more CPU cores and RAM to the server
   - Optimize database settings for larger memory
   - Increase worker processes based on available CPU

2. **Application Optimization**
   - Implement caching for expensive operations
   - Optimize database queries
   - Use asynchronous processing for long-running tasks

## Troubleshooting

### Common Issues

1. **Application Fails to Start**
   - Check error logs: `journalctl -u swot-platform`
   - Verify file permissions and environment variables
   - Test manually: `python app.py`

2. **Database Connection Issues**
   - Verify database service is running
   - Check connection string in `.env`
   - Test connection: `python -c "from app import db; db.engine.connect()"`

3. **Performance Problems**
   - Check server resource usage
   - Look for slow database queries
   - Monitor application logs for bottlenecks

### Diagnostic Commands

```bash
# Check application status
sudo systemctl status swot-platform

# View application logs
sudo journalctl -u swot-platform -f

# Check Nginx status
sudo systemctl status nginx

# Test database connection
mysql -u swotuser -p -e "SELECT 1" swot_platform
# or
psql -U swotuser -c "SELECT 1" swot_platform

# Check disk space
df -h

# Check memory usage
free -m

# Monitor system resources
top
```

## Conclusion

This deployment guide covers the essential steps for deploying and maintaining the SWOT Analysis Platform in various environments. For specific scenarios or troubleshooting, please refer to the platform documentation or contact the development team.
import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Avatar,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  InputAdornment,
  Fab,
  Fade,
  Slide,
  useTheme,
  useMediaQuery,
  alpha,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Assessment as AssessmentIcon,
  School as SchoolIcon,
  Person as PersonIcon,
  Close as CloseIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

// Enhanced Student Card Component
const StudentCard = ({ student, onView, onEdit, onGenerateSWOT }) => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState(null);
  const [isHovered, setIsHovered] = useState(false);

  const handleMenuOpen = (event) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const getPerformanceColor = (score) => {
    if (score >= 80) return theme.palette.success.main;
    if (score >= 60) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  const getPerformanceTrend = (trend) => {
    if (trend > 0) return { icon: TrendingUpIcon, color: theme.palette.success.main };
    if (trend < 0) return { icon: TrendingDownIcon, color: theme.palette.error.main };
    return { icon: null, color: theme.palette.text.secondary };
  };

  const TrendIcon = getPerformanceTrend(student.performanceTrend).icon;

  return (
    <Fade in timeout={300}>
      <Card
        sx={{
          height: '100%',
          cursor: 'pointer',
          transition: 'all 0.3s ease-in-out',
          transform: isHovered ? 'translateY(-4px)' : 'translateY(0)',
          boxShadow: isHovered ? theme.shadows[8] : theme.shadows[1],
          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
          '&:hover': {
            borderColor: theme.palette.primary.main,
          },
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={() => onView(student)}
      >
        <CardContent sx={{ p: 3 }}>
          {/* Header */}
          <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flex: 1 }}>
              <Avatar
                sx={{
                  width: 56,
                  height: 56,
                  bgcolor: theme.palette.primary.main,
                  fontSize: '1.25rem',
                  fontWeight: 600,
                }}
              >
                {student.name.split(' ').map(n => n[0]).join('').toUpperCase()}
              </Avatar>
              <Box sx={{ flex: 1, minWidth: 0 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                  {student.name}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {student.class} • Roll No: {student.rollNumber}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  ID: {student.admissionNumber}
                </Typography>
              </Box>
            </Box>
            <IconButton
              size="small"
              onClick={handleMenuOpen}
              sx={{ 
                opacity: isHovered ? 1 : 0.7,
                transition: 'opacity 0.2s ease-in-out',
              }}
            >
              <MoreVertIcon />
            </IconButton>
          </Box>

          {/* Performance Metrics */}
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="body2" color="text.secondary">
                Overall Performance
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                {TrendIcon && (
                  <TrendIcon 
                    sx={{ 
                      fontSize: 16, 
                      color: getPerformanceTrend(student.performanceTrend).color 
                    }} 
                  />
                )}
                <Typography 
                  variant="body2" 
                  sx={{ 
                    fontWeight: 600,
                    color: getPerformanceColor(student.overallScore)
                  }}
                >
                  {student.overallScore}%
                </Typography>
              </Box>
            </Box>
            <Box
              sx={{
                height: 6,
                borderRadius: 3,
                bgcolor: alpha(getPerformanceColor(student.overallScore), 0.2),
                overflow: 'hidden',
              }}
            >
              <Box
                sx={{
                  height: '100%',
                  width: `${student.overallScore}%`,
                  bgcolor: getPerformanceColor(student.overallScore),
                  borderRadius: 3,
                  transition: 'width 0.5s ease-in-out',
                }}
              />
            </Box>
          </Box>

          {/* Status Chips */}
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
            <Chip
              label={`${student.attendance}% Attendance`}
              size="small"
              color={student.attendance >= 90 ? 'success' : student.attendance >= 75 ? 'warning' : 'error'}
              variant="outlined"
            />
            {student.hasActiveSWOT && (
              <Chip
                label="SWOT Active"
                size="small"
                color="primary"
                variant="filled"
              />
            )}
          </Box>

          {/* Quick Actions */}
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              size="small"
              variant="outlined"
              startIcon={<ViewIcon />}
              onClick={(e) => {
                e.stopPropagation();
                onView(student);
              }}
              sx={{ flex: 1 }}
            >
              View
            </Button>
            <Button
              size="small"
              variant="contained"
              startIcon={<AssessmentIcon />}
              onClick={(e) => {
                e.stopPropagation();
                onGenerateSWOT(student);
              }}
              sx={{ flex: 1 }}
            >
              SWOT
            </Button>
          </Box>
        </CardContent>

        {/* Action Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          onClick={(e) => e.stopPropagation()}
        >
          <MenuItem onClick={() => { onView(student); handleMenuClose(); }}>
            <ViewIcon sx={{ mr: 1 }} />
            View Details
          </MenuItem>
          <MenuItem onClick={() => { onEdit(student); handleMenuClose(); }}>
            <EditIcon sx={{ mr: 1 }} />
            Edit Student
          </MenuItem>
          <MenuItem onClick={() => { onGenerateSWOT(student); handleMenuClose(); }}>
            <AssessmentIcon sx={{ mr: 1 }} />
            Generate SWOT
          </MenuItem>
        </Menu>
      </Card>
    </Fade>
  );
};

// Filter Dialog Component
const FilterDialog = ({ open, onClose, filters, onFiltersChange }) => {
  const { t } = useTranslation(['common']);
  
  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          Filter Students
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Class"
              select
              value={filters.class || ''}
              onChange={(e) => onFiltersChange({ ...filters, class: e.target.value })}
            >
              <MenuItem value="">All Classes</MenuItem>
              <MenuItem value="10-A">Class 10-A</MenuItem>
              <MenuItem value="10-B">Class 10-B</MenuItem>
              <MenuItem value="11-Science">Class 11-Science</MenuItem>
              <MenuItem value="12-Commerce">Class 12-Commerce</MenuItem>
            </TextField>
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Performance"
              select
              value={filters.performance || ''}
              onChange={(e) => onFiltersChange({ ...filters, performance: e.target.value })}
            >
              <MenuItem value="">All Performance</MenuItem>
              <MenuItem value="excellent">Excellent (80%+)</MenuItem>
              <MenuItem value="good">Good (60-79%)</MenuItem>
              <MenuItem value="needs-improvement">Needs Improvement (&lt;60%)</MenuItem>
            </TextField>
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Attendance"
              select
              value={filters.attendance || ''}
              onChange={(e) => onFiltersChange({ ...filters, attendance: e.target.value })}
            >
              <MenuItem value="">All Attendance</MenuItem>
              <MenuItem value="excellent">Excellent (90%+)</MenuItem>
              <MenuItem value="good">Good (75-89%)</MenuItem>
              <MenuItem value="poor">Poor (&lt;75%)</MenuItem>
            </TextField>
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="SWOT Status"
              select
              value={filters.swotStatus || ''}
              onChange={(e) => onFiltersChange({ ...filters, swotStatus: e.target.value })}
            >
              <MenuItem value="">All Students</MenuItem>
              <MenuItem value="active">Has Active SWOT</MenuItem>
              <MenuItem value="pending">SWOT Pending</MenuItem>
            </TextField>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions sx={{ p: 3 }}>
        <Button onClick={() => onFiltersChange({})}>
          Clear All
        </Button>
        <Button variant="contained" onClick={onClose}>
          Apply Filters
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// Main Student Management Component
const StudentManagement = () => {
  const { t } = useTranslation(['common']);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({});
  const [filterDialogOpen, setFilterDialogOpen] = useState(false);

  // Mock data
  useEffect(() => {
    const mockStudents = [
      {
        id: 1,
        name: 'Aarav Sharma',
        class: '10-A',
        rollNumber: '001',
        admissionNumber: 'VM2024001',
        overallScore: 85,
        attendance: 92,
        performanceTrend: 5,
        hasActiveSWOT: true,
      },
      {
        id: 2,
        name: 'Priya Patel',
        class: '10-A',
        rollNumber: '002',
        admissionNumber: 'VM2024002',
        overallScore: 78,
        attendance: 88,
        performanceTrend: -2,
        hasActiveSWOT: false,
      },
      {
        id: 3,
        name: 'Arjun Kumar',
        class: '10-B',
        rollNumber: '003',
        admissionNumber: 'VM2024003',
        overallScore: 92,
        attendance: 95,
        performanceTrend: 8,
        hasActiveSWOT: true,
      },
      {
        id: 4,
        name: 'Ananya Singh',
        class: '11-Science',
        rollNumber: '004',
        admissionNumber: 'VM2024004',
        overallScore: 67,
        attendance: 82,
        performanceTrend: 3,
        hasActiveSWOT: false,
      },
    ];

    setTimeout(() => {
      setStudents(mockStudents);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter students based on search and filters
  const filteredStudents = useMemo(() => {
    return students.filter(student => {
      const matchesSearch = student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           student.admissionNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           student.class.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesClass = !filters.class || student.class === filters.class;
      
      const matchesPerformance = !filters.performance || 
        (filters.performance === 'excellent' && student.overallScore >= 80) ||
        (filters.performance === 'good' && student.overallScore >= 60 && student.overallScore < 80) ||
        (filters.performance === 'needs-improvement' && student.overallScore < 60);
      
      const matchesAttendance = !filters.attendance ||
        (filters.attendance === 'excellent' && student.attendance >= 90) ||
        (filters.attendance === 'good' && student.attendance >= 75 && student.attendance < 90) ||
        (filters.attendance === 'poor' && student.attendance < 75);
      
      const matchesSWOT = !filters.swotStatus ||
        (filters.swotStatus === 'active' && student.hasActiveSWOT) ||
        (filters.swotStatus === 'pending' && !student.hasActiveSWOT);

      return matchesSearch && matchesClass && matchesPerformance && matchesAttendance && matchesSWOT;
    });
  }, [students, searchTerm, filters]);

  const handleViewStudent = (student) => {
    console.log('View student:', student);
  };

  const handleEditStudent = (student) => {
    console.log('Edit student:', student);
  };

  const handleGenerateSWOT = (student) => {
    console.log('Generate SWOT for:', student);
  };

  const handleAddStudent = () => {
    console.log('Add new student');
  };

  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 700, mb: 1 }}>
          Student Management
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage student profiles, track performance, and generate SWOT analyses
        </Typography>
      </Box>

      {/* Search and Filter Bar */}
      <Box sx={{ mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={8}>
            <TextField
              fullWidth
              placeholder="Search students by name, ID, or class..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon color="action" />
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                },
              }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                startIcon={<FilterIcon />}
                onClick={() => setFilterDialogOpen(true)}
                sx={{ borderRadius: 2 }}
              >
                Filter
              </Button>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleAddStudent}
                sx={{ borderRadius: 2 }}
              >
                Add Student
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Results Summary */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="body2" color="text.secondary">
          Showing {filteredStudents.length} of {students.length} students
        </Typography>
      </Box>

      {/* Students Grid */}
      <Grid container spacing={3}>
        {filteredStudents.map((student) => (
          <Grid item xs={12} sm={6} lg={4} key={student.id}>
            <StudentCard
              student={student}
              onView={handleViewStudent}
              onEdit={handleEditStudent}
              onGenerateSWOT={handleGenerateSWOT}
            />
          </Grid>
        ))}
      </Grid>

      {/* Empty State */}
      {filteredStudents.length === 0 && !loading && (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <PersonIcon sx={{ fontSize: 64, color: 'text.disabled', mb: 2 }} />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No students found
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Try adjusting your search criteria or add a new student
          </Typography>
          <Button variant="contained" startIcon={<AddIcon />} onClick={handleAddStudent}>
            Add First Student
          </Button>
        </Box>
      )}

      {/* Floating Action Button for Mobile */}
      {isMobile && (
        <Fab
          color="primary"
          aria-label="add student"
          onClick={handleAddStudent}
          sx={{
            position: 'fixed',
            bottom: 24,
            right: 24,
            zIndex: theme.zIndex.fab,
          }}
        >
          <AddIcon />
        </Fab>
      )}

      {/* Filter Dialog */}
      <FilterDialog
        open={filterDialogOpen}
        onClose={() => setFilterDialogOpen(false)}
        filters={filters}
        onFiltersChange={setFilters}
      />
    </Box>
  );
};

export default StudentManagement;

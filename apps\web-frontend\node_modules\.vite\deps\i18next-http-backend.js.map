{"version": 3, "sources": ["../../../../../node_modules/cross-fetch/dist/browser-ponyfill.js", "../../../../../node_modules/i18next-http-backend/esm/getFetch.cjs", "../../../../../node_modules/i18next-http-backend/esm/utils.js", "../../../../../node_modules/i18next-http-backend/esm/request.js", "../../../../../node_modules/i18next-http-backend/esm/index.js"], "sourcesContent": ["// Save global object in a variable\nvar __global__ =\n(typeof globalThis !== 'undefined' && globalThis) ||\n(typeof self !== 'undefined' && self) ||\n(typeof global !== 'undefined' && global);\n// Create an object that extends from __global__ without the fetch function\nvar __globalThis__ = (function () {\nfunction F() {\nthis.fetch = false;\nthis.DOMException = __global__.DOMException\n}\nF.prototype = __global__; // Needed for feature detection on whatwg-fetch's code\nreturn new F();\n})();\n// Wraps whatwg-fetch with a function scope to hijack the global object\n// \"globalThis\" that's going to be patched\n(function(globalThis) {\n\nvar irrelevant = (function (exports) {\n\n  var global =\n    (typeof globalThis !== 'undefined' && globalThis) ||\n    (typeof self !== 'undefined' && self) ||\n    (typeof global !== 'undefined' && global);\n\n  var support = {\n    searchParams: 'URLSearchParams' in global,\n    iterable: 'Symbol' in global && 'iterator' in Symbol,\n    blob:\n      'FileReader' in global &&\n      'Blob' in global &&\n      (function() {\n        try {\n          new Blob();\n          return true\n        } catch (e) {\n          return false\n        }\n      })(),\n    formData: 'FormData' in global,\n    arrayBuffer: 'ArrayBuffer' in global\n  };\n\n  function isDataView(obj) {\n    return obj && DataView.prototype.isPrototypeOf(obj)\n  }\n\n  if (support.arrayBuffer) {\n    var viewClasses = [\n      '[object Int8Array]',\n      '[object Uint8Array]',\n      '[object Uint8ClampedArray]',\n      '[object Int16Array]',\n      '[object Uint16Array]',\n      '[object Int32Array]',\n      '[object Uint32Array]',\n      '[object Float32Array]',\n      '[object Float64Array]'\n    ];\n\n    var isArrayBufferView =\n      ArrayBuffer.isView ||\n      function(obj) {\n        return obj && viewClasses.indexOf(Object.prototype.toString.call(obj)) > -1\n      };\n  }\n\n  function normalizeName(name) {\n    if (typeof name !== 'string') {\n      name = String(name);\n    }\n    if (/[^a-z0-9\\-#$%&'*+.^_`|~!]/i.test(name) || name === '') {\n      throw new TypeError('Invalid character in header field name: \"' + name + '\"')\n    }\n    return name.toLowerCase()\n  }\n\n  function normalizeValue(value) {\n    if (typeof value !== 'string') {\n      value = String(value);\n    }\n    return value\n  }\n\n  // Build a destructive iterator for the value list\n  function iteratorFor(items) {\n    var iterator = {\n      next: function() {\n        var value = items.shift();\n        return {done: value === undefined, value: value}\n      }\n    };\n\n    if (support.iterable) {\n      iterator[Symbol.iterator] = function() {\n        return iterator\n      };\n    }\n\n    return iterator\n  }\n\n  function Headers(headers) {\n    this.map = {};\n\n    if (headers instanceof Headers) {\n      headers.forEach(function(value, name) {\n        this.append(name, value);\n      }, this);\n    } else if (Array.isArray(headers)) {\n      headers.forEach(function(header) {\n        this.append(header[0], header[1]);\n      }, this);\n    } else if (headers) {\n      Object.getOwnPropertyNames(headers).forEach(function(name) {\n        this.append(name, headers[name]);\n      }, this);\n    }\n  }\n\n  Headers.prototype.append = function(name, value) {\n    name = normalizeName(name);\n    value = normalizeValue(value);\n    var oldValue = this.map[name];\n    this.map[name] = oldValue ? oldValue + ', ' + value : value;\n  };\n\n  Headers.prototype['delete'] = function(name) {\n    delete this.map[normalizeName(name)];\n  };\n\n  Headers.prototype.get = function(name) {\n    name = normalizeName(name);\n    return this.has(name) ? this.map[name] : null\n  };\n\n  Headers.prototype.has = function(name) {\n    return this.map.hasOwnProperty(normalizeName(name))\n  };\n\n  Headers.prototype.set = function(name, value) {\n    this.map[normalizeName(name)] = normalizeValue(value);\n  };\n\n  Headers.prototype.forEach = function(callback, thisArg) {\n    for (var name in this.map) {\n      if (this.map.hasOwnProperty(name)) {\n        callback.call(thisArg, this.map[name], name, this);\n      }\n    }\n  };\n\n  Headers.prototype.keys = function() {\n    var items = [];\n    this.forEach(function(value, name) {\n      items.push(name);\n    });\n    return iteratorFor(items)\n  };\n\n  Headers.prototype.values = function() {\n    var items = [];\n    this.forEach(function(value) {\n      items.push(value);\n    });\n    return iteratorFor(items)\n  };\n\n  Headers.prototype.entries = function() {\n    var items = [];\n    this.forEach(function(value, name) {\n      items.push([name, value]);\n    });\n    return iteratorFor(items)\n  };\n\n  if (support.iterable) {\n    Headers.prototype[Symbol.iterator] = Headers.prototype.entries;\n  }\n\n  function consumed(body) {\n    if (body.bodyUsed) {\n      return Promise.reject(new TypeError('Already read'))\n    }\n    body.bodyUsed = true;\n  }\n\n  function fileReaderReady(reader) {\n    return new Promise(function(resolve, reject) {\n      reader.onload = function() {\n        resolve(reader.result);\n      };\n      reader.onerror = function() {\n        reject(reader.error);\n      };\n    })\n  }\n\n  function readBlobAsArrayBuffer(blob) {\n    var reader = new FileReader();\n    var promise = fileReaderReady(reader);\n    reader.readAsArrayBuffer(blob);\n    return promise\n  }\n\n  function readBlobAsText(blob) {\n    var reader = new FileReader();\n    var promise = fileReaderReady(reader);\n    reader.readAsText(blob);\n    return promise\n  }\n\n  function readArrayBufferAsText(buf) {\n    var view = new Uint8Array(buf);\n    var chars = new Array(view.length);\n\n    for (var i = 0; i < view.length; i++) {\n      chars[i] = String.fromCharCode(view[i]);\n    }\n    return chars.join('')\n  }\n\n  function bufferClone(buf) {\n    if (buf.slice) {\n      return buf.slice(0)\n    } else {\n      var view = new Uint8Array(buf.byteLength);\n      view.set(new Uint8Array(buf));\n      return view.buffer\n    }\n  }\n\n  function Body() {\n    this.bodyUsed = false;\n\n    this._initBody = function(body) {\n      /*\n        fetch-mock wraps the Response object in an ES6 Proxy to\n        provide useful test harness features such as flush. However, on\n        ES5 browsers without fetch or Proxy support pollyfills must be used;\n        the proxy-pollyfill is unable to proxy an attribute unless it exists\n        on the object before the Proxy is created. This change ensures\n        Response.bodyUsed exists on the instance, while maintaining the\n        semantic of setting Request.bodyUsed in the constructor before\n        _initBody is called.\n      */\n      this.bodyUsed = this.bodyUsed;\n      this._bodyInit = body;\n      if (!body) {\n        this._bodyText = '';\n      } else if (typeof body === 'string') {\n        this._bodyText = body;\n      } else if (support.blob && Blob.prototype.isPrototypeOf(body)) {\n        this._bodyBlob = body;\n      } else if (support.formData && FormData.prototype.isPrototypeOf(body)) {\n        this._bodyFormData = body;\n      } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n        this._bodyText = body.toString();\n      } else if (support.arrayBuffer && support.blob && isDataView(body)) {\n        this._bodyArrayBuffer = bufferClone(body.buffer);\n        // IE 10-11 can't handle a DataView body.\n        this._bodyInit = new Blob([this._bodyArrayBuffer]);\n      } else if (support.arrayBuffer && (ArrayBuffer.prototype.isPrototypeOf(body) || isArrayBufferView(body))) {\n        this._bodyArrayBuffer = bufferClone(body);\n      } else {\n        this._bodyText = body = Object.prototype.toString.call(body);\n      }\n\n      if (!this.headers.get('content-type')) {\n        if (typeof body === 'string') {\n          this.headers.set('content-type', 'text/plain;charset=UTF-8');\n        } else if (this._bodyBlob && this._bodyBlob.type) {\n          this.headers.set('content-type', this._bodyBlob.type);\n        } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n          this.headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8');\n        }\n      }\n    };\n\n    if (support.blob) {\n      this.blob = function() {\n        var rejected = consumed(this);\n        if (rejected) {\n          return rejected\n        }\n\n        if (this._bodyBlob) {\n          return Promise.resolve(this._bodyBlob)\n        } else if (this._bodyArrayBuffer) {\n          return Promise.resolve(new Blob([this._bodyArrayBuffer]))\n        } else if (this._bodyFormData) {\n          throw new Error('could not read FormData body as blob')\n        } else {\n          return Promise.resolve(new Blob([this._bodyText]))\n        }\n      };\n\n      this.arrayBuffer = function() {\n        if (this._bodyArrayBuffer) {\n          var isConsumed = consumed(this);\n          if (isConsumed) {\n            return isConsumed\n          }\n          if (ArrayBuffer.isView(this._bodyArrayBuffer)) {\n            return Promise.resolve(\n              this._bodyArrayBuffer.buffer.slice(\n                this._bodyArrayBuffer.byteOffset,\n                this._bodyArrayBuffer.byteOffset + this._bodyArrayBuffer.byteLength\n              )\n            )\n          } else {\n            return Promise.resolve(this._bodyArrayBuffer)\n          }\n        } else {\n          return this.blob().then(readBlobAsArrayBuffer)\n        }\n      };\n    }\n\n    this.text = function() {\n      var rejected = consumed(this);\n      if (rejected) {\n        return rejected\n      }\n\n      if (this._bodyBlob) {\n        return readBlobAsText(this._bodyBlob)\n      } else if (this._bodyArrayBuffer) {\n        return Promise.resolve(readArrayBufferAsText(this._bodyArrayBuffer))\n      } else if (this._bodyFormData) {\n        throw new Error('could not read FormData body as text')\n      } else {\n        return Promise.resolve(this._bodyText)\n      }\n    };\n\n    if (support.formData) {\n      this.formData = function() {\n        return this.text().then(decode)\n      };\n    }\n\n    this.json = function() {\n      return this.text().then(JSON.parse)\n    };\n\n    return this\n  }\n\n  // HTTP methods whose capitalization should be normalized\n  var methods = ['DELETE', 'GET', 'HEAD', 'OPTIONS', 'POST', 'PUT'];\n\n  function normalizeMethod(method) {\n    var upcased = method.toUpperCase();\n    return methods.indexOf(upcased) > -1 ? upcased : method\n  }\n\n  function Request(input, options) {\n    if (!(this instanceof Request)) {\n      throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.')\n    }\n\n    options = options || {};\n    var body = options.body;\n\n    if (input instanceof Request) {\n      if (input.bodyUsed) {\n        throw new TypeError('Already read')\n      }\n      this.url = input.url;\n      this.credentials = input.credentials;\n      if (!options.headers) {\n        this.headers = new Headers(input.headers);\n      }\n      this.method = input.method;\n      this.mode = input.mode;\n      this.signal = input.signal;\n      if (!body && input._bodyInit != null) {\n        body = input._bodyInit;\n        input.bodyUsed = true;\n      }\n    } else {\n      this.url = String(input);\n    }\n\n    this.credentials = options.credentials || this.credentials || 'same-origin';\n    if (options.headers || !this.headers) {\n      this.headers = new Headers(options.headers);\n    }\n    this.method = normalizeMethod(options.method || this.method || 'GET');\n    this.mode = options.mode || this.mode || null;\n    this.signal = options.signal || this.signal;\n    this.referrer = null;\n\n    if ((this.method === 'GET' || this.method === 'HEAD') && body) {\n      throw new TypeError('Body not allowed for GET or HEAD requests')\n    }\n    this._initBody(body);\n\n    if (this.method === 'GET' || this.method === 'HEAD') {\n      if (options.cache === 'no-store' || options.cache === 'no-cache') {\n        // Search for a '_' parameter in the query string\n        var reParamSearch = /([?&])_=[^&]*/;\n        if (reParamSearch.test(this.url)) {\n          // If it already exists then set the value with the current time\n          this.url = this.url.replace(reParamSearch, '$1_=' + new Date().getTime());\n        } else {\n          // Otherwise add a new '_' parameter to the end with the current time\n          var reQueryString = /\\?/;\n          this.url += (reQueryString.test(this.url) ? '&' : '?') + '_=' + new Date().getTime();\n        }\n      }\n    }\n  }\n\n  Request.prototype.clone = function() {\n    return new Request(this, {body: this._bodyInit})\n  };\n\n  function decode(body) {\n    var form = new FormData();\n    body\n      .trim()\n      .split('&')\n      .forEach(function(bytes) {\n        if (bytes) {\n          var split = bytes.split('=');\n          var name = split.shift().replace(/\\+/g, ' ');\n          var value = split.join('=').replace(/\\+/g, ' ');\n          form.append(decodeURIComponent(name), decodeURIComponent(value));\n        }\n      });\n    return form\n  }\n\n  function parseHeaders(rawHeaders) {\n    var headers = new Headers();\n    // Replace instances of \\r\\n and \\n followed by at least one space or horizontal tab with a space\n    // https://tools.ietf.org/html/rfc7230#section-3.2\n    var preProcessedHeaders = rawHeaders.replace(/\\r?\\n[\\t ]+/g, ' ');\n    // Avoiding split via regex to work around a common IE11 bug with the core-js 3.6.0 regex polyfill\n    // https://github.com/github/fetch/issues/748\n    // https://github.com/zloirock/core-js/issues/751\n    preProcessedHeaders\n      .split('\\r')\n      .map(function(header) {\n        return header.indexOf('\\n') === 0 ? header.substr(1, header.length) : header\n      })\n      .forEach(function(line) {\n        var parts = line.split(':');\n        var key = parts.shift().trim();\n        if (key) {\n          var value = parts.join(':').trim();\n          headers.append(key, value);\n        }\n      });\n    return headers\n  }\n\n  Body.call(Request.prototype);\n\n  function Response(bodyInit, options) {\n    if (!(this instanceof Response)) {\n      throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.')\n    }\n    if (!options) {\n      options = {};\n    }\n\n    this.type = 'default';\n    this.status = options.status === undefined ? 200 : options.status;\n    this.ok = this.status >= 200 && this.status < 300;\n    this.statusText = options.statusText === undefined ? '' : '' + options.statusText;\n    this.headers = new Headers(options.headers);\n    this.url = options.url || '';\n    this._initBody(bodyInit);\n  }\n\n  Body.call(Response.prototype);\n\n  Response.prototype.clone = function() {\n    return new Response(this._bodyInit, {\n      status: this.status,\n      statusText: this.statusText,\n      headers: new Headers(this.headers),\n      url: this.url\n    })\n  };\n\n  Response.error = function() {\n    var response = new Response(null, {status: 0, statusText: ''});\n    response.type = 'error';\n    return response\n  };\n\n  var redirectStatuses = [301, 302, 303, 307, 308];\n\n  Response.redirect = function(url, status) {\n    if (redirectStatuses.indexOf(status) === -1) {\n      throw new RangeError('Invalid status code')\n    }\n\n    return new Response(null, {status: status, headers: {location: url}})\n  };\n\n  exports.DOMException = global.DOMException;\n  try {\n    new exports.DOMException();\n  } catch (err) {\n    exports.DOMException = function(message, name) {\n      this.message = message;\n      this.name = name;\n      var error = Error(message);\n      this.stack = error.stack;\n    };\n    exports.DOMException.prototype = Object.create(Error.prototype);\n    exports.DOMException.prototype.constructor = exports.DOMException;\n  }\n\n  function fetch(input, init) {\n    return new Promise(function(resolve, reject) {\n      var request = new Request(input, init);\n\n      if (request.signal && request.signal.aborted) {\n        return reject(new exports.DOMException('Aborted', 'AbortError'))\n      }\n\n      var xhr = new XMLHttpRequest();\n\n      function abortXhr() {\n        xhr.abort();\n      }\n\n      xhr.onload = function() {\n        var options = {\n          status: xhr.status,\n          statusText: xhr.statusText,\n          headers: parseHeaders(xhr.getAllResponseHeaders() || '')\n        };\n        options.url = 'responseURL' in xhr ? xhr.responseURL : options.headers.get('X-Request-URL');\n        var body = 'response' in xhr ? xhr.response : xhr.responseText;\n        setTimeout(function() {\n          resolve(new Response(body, options));\n        }, 0);\n      };\n\n      xhr.onerror = function() {\n        setTimeout(function() {\n          reject(new TypeError('Network request failed'));\n        }, 0);\n      };\n\n      xhr.ontimeout = function() {\n        setTimeout(function() {\n          reject(new TypeError('Network request failed'));\n        }, 0);\n      };\n\n      xhr.onabort = function() {\n        setTimeout(function() {\n          reject(new exports.DOMException('Aborted', 'AbortError'));\n        }, 0);\n      };\n\n      function fixUrl(url) {\n        try {\n          return url === '' && global.location.href ? global.location.href : url\n        } catch (e) {\n          return url\n        }\n      }\n\n      xhr.open(request.method, fixUrl(request.url), true);\n\n      if (request.credentials === 'include') {\n        xhr.withCredentials = true;\n      } else if (request.credentials === 'omit') {\n        xhr.withCredentials = false;\n      }\n\n      if ('responseType' in xhr) {\n        if (support.blob) {\n          xhr.responseType = 'blob';\n        } else if (\n          support.arrayBuffer &&\n          request.headers.get('Content-Type') &&\n          request.headers.get('Content-Type').indexOf('application/octet-stream') !== -1\n        ) {\n          xhr.responseType = 'arraybuffer';\n        }\n      }\n\n      if (init && typeof init.headers === 'object' && !(init.headers instanceof Headers)) {\n        Object.getOwnPropertyNames(init.headers).forEach(function(name) {\n          xhr.setRequestHeader(name, normalizeValue(init.headers[name]));\n        });\n      } else {\n        request.headers.forEach(function(value, name) {\n          xhr.setRequestHeader(name, value);\n        });\n      }\n\n      if (request.signal) {\n        request.signal.addEventListener('abort', abortXhr);\n\n        xhr.onreadystatechange = function() {\n          // DONE (success or failure)\n          if (xhr.readyState === 4) {\n            request.signal.removeEventListener('abort', abortXhr);\n          }\n        };\n      }\n\n      xhr.send(typeof request._bodyInit === 'undefined' ? null : request._bodyInit);\n    })\n  }\n\n  fetch.polyfill = true;\n\n  if (!global.fetch) {\n    global.fetch = fetch;\n    global.Headers = Headers;\n    global.Request = Request;\n    global.Response = Response;\n  }\n\n  exports.Headers = Headers;\n  exports.Request = Request;\n  exports.Response = Response;\n  exports.fetch = fetch;\n\n  return exports;\n\n})({});\n})(__globalThis__);\n// This is a ponyfill, so...\n__globalThis__.fetch.ponyfill = true;\ndelete __globalThis__.fetch.polyfill;\n// Choose between native implementation (__global__) or custom implementation (__globalThis__)\nvar ctx = __global__.fetch ? __global__ : __globalThis__;\nexports = ctx.fetch // To enable: import fetch from 'cross-fetch'\nexports.default = ctx.fetch // For TypeScript consumers without esModuleInterop.\nexports.fetch = ctx.fetch // To enable: import {fetch} from 'cross-fetch'\nexports.Headers = ctx.Headers\nexports.Request = ctx.Request\nexports.Response = ctx.Response\nmodule.exports = exports\n", "var fetchApi = typeof fetch === 'function' ? fetch : undefined\nif (typeof global !== 'undefined' && global.fetch) {\n  fetchApi = global.fetch\n} else if (typeof window !== 'undefined' && window.fetch) {\n  fetchApi = window.fetch\n}\n\nif (typeof require !== 'undefined' && typeof window === 'undefined') {\n  var f = fetchApi || require('cross-fetch')\n  if (f.default) f = f.default\n  exports.default = f\n  module.exports = exports.default\n}\n", "function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nvar arr = [];\nvar each = arr.forEach;\nvar slice = arr.slice;\nexport function defaults(obj) {\n  each.call(slice.call(arguments, 1), function (source) {\n    if (source) {\n      for (var prop in source) {\n        if (obj[prop] === undefined) obj[prop] = source[prop];\n      }\n    }\n  });\n  return obj;\n}\nexport function hasXMLHttpRequest() {\n  return typeof XMLHttpRequest === 'function' || (typeof XMLHttpRequest === \"undefined\" ? \"undefined\" : _typeof(XMLHttpRequest)) === 'object';\n}\nfunction isPromise(maybePromise) {\n  return !!maybePromise && typeof maybePromise.then === 'function';\n}\nexport function makePromise(maybePromise) {\n  if (isPromise(maybePromise)) {\n    return maybePromise;\n  }\n  return Promise.resolve(maybePromise);\n}", "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nimport { hasXMLHttpRequest } from './utils.js';\nimport * as fetchNode from './getFetch.cjs';\nvar fetchApi = typeof fetch === 'function' ? fetch : undefined;\nif (typeof global !== 'undefined' && global.fetch) {\n  fetchApi = global.fetch;\n} else if (typeof window !== 'undefined' && window.fetch) {\n  fetchApi = window.fetch;\n}\nvar XmlHttpRequestApi;\nif (hasXMLHttpRequest()) {\n  if (typeof global !== 'undefined' && global.XMLHttpRequest) {\n    XmlHttpRequestApi = global.XMLHttpRequest;\n  } else if (typeof window !== 'undefined' && window.XMLHttpRequest) {\n    XmlHttpRequestApi = window.XMLHttpRequest;\n  }\n}\nvar ActiveXObjectApi;\nif (typeof ActiveXObject === 'function') {\n  if (typeof global !== 'undefined' && global.ActiveXObject) {\n    ActiveXObjectApi = global.ActiveXObject;\n  } else if (typeof window !== 'undefined' && window.ActiveXObject) {\n    ActiveXObjectApi = window.ActiveXObject;\n  }\n}\nif (!fetchApi && fetchNode && !XmlHttpRequestApi && !ActiveXObjectApi) fetchApi = fetchNode.default || fetchNode;\nif (typeof fetchApi !== 'function') fetchApi = undefined;\nvar addQueryString = function addQueryString(url, params) {\n  if (params && _typeof(params) === 'object') {\n    var queryString = '';\n    for (var paramName in params) {\n      queryString += '&' + encodeURIComponent(paramName) + '=' + encodeURIComponent(params[paramName]);\n    }\n    if (!queryString) return url;\n    url = url + (url.indexOf('?') !== -1 ? '&' : '?') + queryString.slice(1);\n  }\n  return url;\n};\nvar fetchIt = function fetchIt(url, fetchOptions, callback, altFetch) {\n  var resolver = function resolver(response) {\n    if (!response.ok) return callback(response.statusText || 'Error', {\n      status: response.status\n    });\n    response.text().then(function (data) {\n      callback(null, {\n        status: response.status,\n        data: data\n      });\n    }).catch(callback);\n  };\n  if (altFetch) {\n    var altResponse = altFetch(url, fetchOptions);\n    if (altResponse instanceof Promise) {\n      altResponse.then(resolver).catch(callback);\n      return;\n    }\n  }\n  if (typeof fetch === 'function') {\n    fetch(url, fetchOptions).then(resolver).catch(callback);\n  } else {\n    fetchApi(url, fetchOptions).then(resolver).catch(callback);\n  }\n};\nvar omitFetchOptions = false;\nvar requestWithFetch = function requestWithFetch(options, url, payload, callback) {\n  if (options.queryStringParams) {\n    url = addQueryString(url, options.queryStringParams);\n  }\n  var headers = _objectSpread({}, typeof options.customHeaders === 'function' ? options.customHeaders() : options.customHeaders);\n  if (typeof window === 'undefined' && typeof global !== 'undefined' && typeof global.process !== 'undefined' && global.process.versions && global.process.versions.node) {\n    headers['User-Agent'] = \"i18next-http-backend (node/\".concat(global.process.version, \"; \").concat(global.process.platform, \" \").concat(global.process.arch, \")\");\n  }\n  if (payload) headers['Content-Type'] = 'application/json';\n  var reqOptions = typeof options.requestOptions === 'function' ? options.requestOptions(payload) : options.requestOptions;\n  var fetchOptions = _objectSpread({\n    method: payload ? 'POST' : 'GET',\n    body: payload ? options.stringify(payload) : undefined,\n    headers: headers\n  }, omitFetchOptions ? {} : reqOptions);\n  var altFetch = typeof options.alternateFetch === 'function' && options.alternateFetch.length >= 1 ? options.alternateFetch : undefined;\n  try {\n    fetchIt(url, fetchOptions, callback, altFetch);\n  } catch (e) {\n    if (!reqOptions || Object.keys(reqOptions).length === 0 || !e.message || e.message.indexOf('not implemented') < 0) {\n      return callback(e);\n    }\n    try {\n      Object.keys(reqOptions).forEach(function (opt) {\n        delete fetchOptions[opt];\n      });\n      fetchIt(url, fetchOptions, callback, altFetch);\n      omitFetchOptions = true;\n    } catch (err) {\n      callback(err);\n    }\n  }\n};\nvar requestWithXmlHttpRequest = function requestWithXmlHttpRequest(options, url, payload, callback) {\n  if (payload && _typeof(payload) === 'object') {\n    payload = addQueryString('', payload).slice(1);\n  }\n  if (options.queryStringParams) {\n    url = addQueryString(url, options.queryStringParams);\n  }\n  try {\n    var x;\n    if (XmlHttpRequestApi) {\n      x = new XmlHttpRequestApi();\n    } else {\n      x = new ActiveXObjectApi('MSXML2.XMLHTTP.3.0');\n    }\n    x.open(payload ? 'POST' : 'GET', url, 1);\n    if (!options.crossDomain) {\n      x.setRequestHeader('X-Requested-With', 'XMLHttpRequest');\n    }\n    x.withCredentials = !!options.withCredentials;\n    if (payload) {\n      x.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');\n    }\n    if (x.overrideMimeType) {\n      x.overrideMimeType('application/json');\n    }\n    var h = options.customHeaders;\n    h = typeof h === 'function' ? h() : h;\n    if (h) {\n      for (var i in h) {\n        x.setRequestHeader(i, h[i]);\n      }\n    }\n    x.onreadystatechange = function () {\n      x.readyState > 3 && callback(x.status >= 400 ? x.statusText : null, {\n        status: x.status,\n        data: x.responseText\n      });\n    };\n    x.send(payload);\n  } catch (e) {\n    console && console.log(e);\n  }\n};\nvar request = function request(options, url, payload, callback) {\n  if (typeof payload === 'function') {\n    callback = payload;\n    payload = undefined;\n  }\n  callback = callback || function () {};\n  if (fetchApi && url.indexOf('file:') !== 0) {\n    return requestWithFetch(options, url, payload, callback);\n  }\n  if (hasXMLHttpRequest() || typeof ActiveXObject === 'function') {\n    return requestWithXmlHttpRequest(options, url, payload, callback);\n  }\n  callback(new Error('No fetch and no xhr implementation found!'));\n};\nexport default request;", "function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { makePromise } from './utils.js';\nimport request from './request.js';\nvar getDefaults = function getDefaults() {\n  return {\n    loadPath: '/locales/{{lng}}/{{ns}}.json',\n    addPath: '/locales/add/{{lng}}/{{ns}}',\n    parse: function parse(data) {\n      return JSON.parse(data);\n    },\n    stringify: JSON.stringify,\n    parsePayload: function parsePayload(namespace, key, fallbackValue) {\n      return _defineProperty({}, key, fallbackValue || '');\n    },\n    parseLoadPayload: function parseLoadPayload(languages, namespaces) {\n      return undefined;\n    },\n    request: request,\n    reloadInterval: typeof window !== 'undefined' ? false : 60 * 60 * 1000,\n    customHeaders: {},\n    queryStringParams: {},\n    crossDomain: false,\n    withCredentials: false,\n    overrideMimeType: false,\n    requestOptions: {\n      mode: 'cors',\n      credentials: 'same-origin',\n      cache: 'default'\n    }\n  };\n};\nvar Backend = function () {\n  function Backend(services) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var allOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    _classCallCheck(this, Backend);\n    this.services = services;\n    this.options = options;\n    this.allOptions = allOptions;\n    this.type = 'backend';\n    this.init(services, options, allOptions);\n  }\n  return _createClass(Backend, [{\n    key: \"init\",\n    value: function init(services) {\n      var _this = this;\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var allOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n      this.services = services;\n      this.options = _objectSpread(_objectSpread(_objectSpread({}, getDefaults()), this.options || {}), options);\n      this.allOptions = allOptions;\n      if (this.services && this.options.reloadInterval) {\n        var timer = setInterval(function () {\n          return _this.reload();\n        }, this.options.reloadInterval);\n        if (_typeof(timer) === 'object' && typeof timer.unref === 'function') timer.unref();\n      }\n    }\n  }, {\n    key: \"readMulti\",\n    value: function readMulti(languages, namespaces, callback) {\n      this._readAny(languages, languages, namespaces, namespaces, callback);\n    }\n  }, {\n    key: \"read\",\n    value: function read(language, namespace, callback) {\n      this._readAny([language], language, [namespace], namespace, callback);\n    }\n  }, {\n    key: \"_readAny\",\n    value: function _readAny(languages, loadUrlLanguages, namespaces, loadUrlNamespaces, callback) {\n      var _this2 = this;\n      var loadPath = this.options.loadPath;\n      if (typeof this.options.loadPath === 'function') {\n        loadPath = this.options.loadPath(languages, namespaces);\n      }\n      loadPath = makePromise(loadPath);\n      loadPath.then(function (resolvedLoadPath) {\n        if (!resolvedLoadPath) return callback(null, {});\n        var url = _this2.services.interpolator.interpolate(resolvedLoadPath, {\n          lng: languages.join('+'),\n          ns: namespaces.join('+')\n        });\n        _this2.loadUrl(url, callback, loadUrlLanguages, loadUrlNamespaces);\n      });\n    }\n  }, {\n    key: \"loadUrl\",\n    value: function loadUrl(url, callback, languages, namespaces) {\n      var _this3 = this;\n      var lng = typeof languages === 'string' ? [languages] : languages;\n      var ns = typeof namespaces === 'string' ? [namespaces] : namespaces;\n      var payload = this.options.parseLoadPayload(lng, ns);\n      this.options.request(this.options, url, payload, function (err, res) {\n        if (res && (res.status >= 500 && res.status < 600 || !res.status)) return callback('failed loading ' + url + '; status code: ' + res.status, true);\n        if (res && res.status >= 400 && res.status < 500) return callback('failed loading ' + url + '; status code: ' + res.status, false);\n        if (!res && err && err.message) {\n          var errorMessage = err.message.toLowerCase();\n          var isNetworkError = ['failed', 'fetch', 'network', 'load'].find(function (term) {\n            return errorMessage.indexOf(term) > -1;\n          });\n          if (isNetworkError) {\n            return callback('failed loading ' + url + ': ' + err.message, true);\n          }\n        }\n        if (err) return callback(err, false);\n        var ret, parseErr;\n        try {\n          if (typeof res.data === 'string') {\n            ret = _this3.options.parse(res.data, languages, namespaces);\n          } else {\n            ret = res.data;\n          }\n        } catch (e) {\n          parseErr = 'failed parsing ' + url + ' to json';\n        }\n        if (parseErr) return callback(parseErr, false);\n        callback(null, ret);\n      });\n    }\n  }, {\n    key: \"create\",\n    value: function create(languages, namespace, key, fallbackValue, callback) {\n      var _this4 = this;\n      if (!this.options.addPath) return;\n      if (typeof languages === 'string') languages = [languages];\n      var payload = this.options.parsePayload(namespace, key, fallbackValue);\n      var finished = 0;\n      var dataArray = [];\n      var resArray = [];\n      languages.forEach(function (lng) {\n        var addPath = _this4.options.addPath;\n        if (typeof _this4.options.addPath === 'function') {\n          addPath = _this4.options.addPath(lng, namespace);\n        }\n        var url = _this4.services.interpolator.interpolate(addPath, {\n          lng: lng,\n          ns: namespace\n        });\n        _this4.options.request(_this4.options, url, payload, function (data, res) {\n          finished += 1;\n          dataArray.push(data);\n          resArray.push(res);\n          if (finished === languages.length) {\n            if (typeof callback === 'function') callback(dataArray, resArray);\n          }\n        });\n      });\n    }\n  }, {\n    key: \"reload\",\n    value: function reload() {\n      var _this5 = this;\n      var _this$services = this.services,\n        backendConnector = _this$services.backendConnector,\n        languageUtils = _this$services.languageUtils,\n        logger = _this$services.logger;\n      var currentLanguage = backendConnector.language;\n      if (currentLanguage && currentLanguage.toLowerCase() === 'cimode') return;\n      var toLoad = [];\n      var append = function append(lng) {\n        var lngs = languageUtils.toResolveHierarchy(lng);\n        lngs.forEach(function (l) {\n          if (toLoad.indexOf(l) < 0) toLoad.push(l);\n        });\n      };\n      append(currentLanguage);\n      if (this.allOptions.preload) this.allOptions.preload.forEach(function (l) {\n        return append(l);\n      });\n      toLoad.forEach(function (lng) {\n        _this5.allOptions.ns.forEach(function (ns) {\n          backendConnector.read(lng, ns, 'read', null, null, function (err, data) {\n            if (err) logger.warn(\"loading namespace \".concat(ns, \" for language \").concat(lng, \" failed\"), err);\n            if (!err && data) logger.log(\"loaded namespace \".concat(ns, \" for language \").concat(lng), data);\n            backendConnector.loaded(\"\".concat(lng, \"|\").concat(ns), err, data);\n          });\n        });\n      });\n    }\n  }]);\n}();\nBackend.type = 'backend';\nexport default Backend;"], "mappings": ";;;;;;;AAAA;AAAA;AACA,QAAI,aACH,OAAO,eAAe,eAAe,cACrC,OAAO,SAAS,eAAe,QAC/B,OAAO,WAAW,eAAe;AAElC,QAAI,iBAAkB,WAAY;AAClC,eAAS,IAAI;AACb,aAAK,QAAQ;AACb,aAAK,eAAe,WAAW;AAAA,MAC/B;AACA,QAAE,YAAY;AACd,aAAO,IAAI,EAAE;AAAA,IACb,EAAG;AAGH,KAAC,SAASA,aAAY;AAEtB,UAAI,aAAc,SAAUC,UAAS;AAEnC,YAAIC,UACD,OAAOF,gBAAe,eAAeA,eACrC,OAAO,SAAS,eAAe,QAC/B,OAAOE,YAAW,eAAeA;AAEpC,YAAI,UAAU;AAAA,UACZ,cAAc,qBAAqBA;AAAA,UACnC,UAAU,YAAYA,WAAU,cAAc;AAAA,UAC9C,MACE,gBAAgBA,WAChB,UAAUA,WACT,WAAW;AACV,gBAAI;AACF,kBAAI,KAAK;AACT,qBAAO;AAAA,YACT,SAAS,GAAG;AACV,qBAAO;AAAA,YACT;AAAA,UACF,EAAG;AAAA,UACL,UAAU,cAAcA;AAAA,UACxB,aAAa,iBAAiBA;AAAA,QAChC;AAEA,iBAAS,WAAW,KAAK;AACvB,iBAAO,OAAO,SAAS,UAAU,cAAc,GAAG;AAAA,QACpD;AAEA,YAAI,QAAQ,aAAa;AACvB,cAAI,cAAc;AAAA,YAChB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAEA,cAAI,oBACF,YAAY,UACZ,SAAS,KAAK;AACZ,mBAAO,OAAO,YAAY,QAAQ,OAAO,UAAU,SAAS,KAAK,GAAG,CAAC,IAAI;AAAA,UAC3E;AAAA,QACJ;AAEA,iBAAS,cAAc,MAAM;AAC3B,cAAI,OAAO,SAAS,UAAU;AAC5B,mBAAO,OAAO,IAAI;AAAA,UACpB;AACA,cAAI,6BAA6B,KAAK,IAAI,KAAK,SAAS,IAAI;AAC1D,kBAAM,IAAI,UAAU,8CAA8C,OAAO,GAAG;AAAA,UAC9E;AACA,iBAAO,KAAK,YAAY;AAAA,QAC1B;AAEA,iBAAS,eAAe,OAAO;AAC7B,cAAI,OAAO,UAAU,UAAU;AAC7B,oBAAQ,OAAO,KAAK;AAAA,UACtB;AACA,iBAAO;AAAA,QACT;AAGA,iBAAS,YAAY,OAAO;AAC1B,cAAI,WAAW;AAAA,YACb,MAAM,WAAW;AACf,kBAAI,QAAQ,MAAM,MAAM;AACxB,qBAAO,EAAC,MAAM,UAAU,QAAW,MAAY;AAAA,YACjD;AAAA,UACF;AAEA,cAAI,QAAQ,UAAU;AACpB,qBAAS,OAAO,QAAQ,IAAI,WAAW;AACrC,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,iBAAS,QAAQ,SAAS;AACxB,eAAK,MAAM,CAAC;AAEZ,cAAI,mBAAmB,SAAS;AAC9B,oBAAQ,QAAQ,SAAS,OAAO,MAAM;AACpC,mBAAK,OAAO,MAAM,KAAK;AAAA,YACzB,GAAG,IAAI;AAAA,UACT,WAAW,MAAM,QAAQ,OAAO,GAAG;AACjC,oBAAQ,QAAQ,SAAS,QAAQ;AAC/B,mBAAK,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,YAClC,GAAG,IAAI;AAAA,UACT,WAAW,SAAS;AAClB,mBAAO,oBAAoB,OAAO,EAAE,QAAQ,SAAS,MAAM;AACzD,mBAAK,OAAO,MAAM,QAAQ,IAAI,CAAC;AAAA,YACjC,GAAG,IAAI;AAAA,UACT;AAAA,QACF;AAEA,gBAAQ,UAAU,SAAS,SAAS,MAAM,OAAO;AAC/C,iBAAO,cAAc,IAAI;AACzB,kBAAQ,eAAe,KAAK;AAC5B,cAAI,WAAW,KAAK,IAAI,IAAI;AAC5B,eAAK,IAAI,IAAI,IAAI,WAAW,WAAW,OAAO,QAAQ;AAAA,QACxD;AAEA,gBAAQ,UAAU,QAAQ,IAAI,SAAS,MAAM;AAC3C,iBAAO,KAAK,IAAI,cAAc,IAAI,CAAC;AAAA,QACrC;AAEA,gBAAQ,UAAU,MAAM,SAAS,MAAM;AACrC,iBAAO,cAAc,IAAI;AACzB,iBAAO,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI;AAAA,QAC3C;AAEA,gBAAQ,UAAU,MAAM,SAAS,MAAM;AACrC,iBAAO,KAAK,IAAI,eAAe,cAAc,IAAI,CAAC;AAAA,QACpD;AAEA,gBAAQ,UAAU,MAAM,SAAS,MAAM,OAAO;AAC5C,eAAK,IAAI,cAAc,IAAI,CAAC,IAAI,eAAe,KAAK;AAAA,QACtD;AAEA,gBAAQ,UAAU,UAAU,SAAS,UAAU,SAAS;AACtD,mBAAS,QAAQ,KAAK,KAAK;AACzB,gBAAI,KAAK,IAAI,eAAe,IAAI,GAAG;AACjC,uBAAS,KAAK,SAAS,KAAK,IAAI,IAAI,GAAG,MAAM,IAAI;AAAA,YACnD;AAAA,UACF;AAAA,QACF;AAEA,gBAAQ,UAAU,OAAO,WAAW;AAClC,cAAI,QAAQ,CAAC;AACb,eAAK,QAAQ,SAAS,OAAO,MAAM;AACjC,kBAAM,KAAK,IAAI;AAAA,UACjB,CAAC;AACD,iBAAO,YAAY,KAAK;AAAA,QAC1B;AAEA,gBAAQ,UAAU,SAAS,WAAW;AACpC,cAAI,QAAQ,CAAC;AACb,eAAK,QAAQ,SAAS,OAAO;AAC3B,kBAAM,KAAK,KAAK;AAAA,UAClB,CAAC;AACD,iBAAO,YAAY,KAAK;AAAA,QAC1B;AAEA,gBAAQ,UAAU,UAAU,WAAW;AACrC,cAAI,QAAQ,CAAC;AACb,eAAK,QAAQ,SAAS,OAAO,MAAM;AACjC,kBAAM,KAAK,CAAC,MAAM,KAAK,CAAC;AAAA,UAC1B,CAAC;AACD,iBAAO,YAAY,KAAK;AAAA,QAC1B;AAEA,YAAI,QAAQ,UAAU;AACpB,kBAAQ,UAAU,OAAO,QAAQ,IAAI,QAAQ,UAAU;AAAA,QACzD;AAEA,iBAAS,SAAS,MAAM;AACtB,cAAI,KAAK,UAAU;AACjB,mBAAO,QAAQ,OAAO,IAAI,UAAU,cAAc,CAAC;AAAA,UACrD;AACA,eAAK,WAAW;AAAA,QAClB;AAEA,iBAAS,gBAAgB,QAAQ;AAC/B,iBAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,mBAAO,SAAS,WAAW;AACzB,sBAAQ,OAAO,MAAM;AAAA,YACvB;AACA,mBAAO,UAAU,WAAW;AAC1B,qBAAO,OAAO,KAAK;AAAA,YACrB;AAAA,UACF,CAAC;AAAA,QACH;AAEA,iBAAS,sBAAsB,MAAM;AACnC,cAAI,SAAS,IAAI,WAAW;AAC5B,cAAI,UAAU,gBAAgB,MAAM;AACpC,iBAAO,kBAAkB,IAAI;AAC7B,iBAAO;AAAA,QACT;AAEA,iBAAS,eAAe,MAAM;AAC5B,cAAI,SAAS,IAAI,WAAW;AAC5B,cAAI,UAAU,gBAAgB,MAAM;AACpC,iBAAO,WAAW,IAAI;AACtB,iBAAO;AAAA,QACT;AAEA,iBAAS,sBAAsB,KAAK;AAClC,cAAI,OAAO,IAAI,WAAW,GAAG;AAC7B,cAAI,QAAQ,IAAI,MAAM,KAAK,MAAM;AAEjC,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,kBAAM,CAAC,IAAI,OAAO,aAAa,KAAK,CAAC,CAAC;AAAA,UACxC;AACA,iBAAO,MAAM,KAAK,EAAE;AAAA,QACtB;AAEA,iBAAS,YAAY,KAAK;AACxB,cAAI,IAAI,OAAO;AACb,mBAAO,IAAI,MAAM,CAAC;AAAA,UACpB,OAAO;AACL,gBAAI,OAAO,IAAI,WAAW,IAAI,UAAU;AACxC,iBAAK,IAAI,IAAI,WAAW,GAAG,CAAC;AAC5B,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAEA,iBAAS,OAAO;AACd,eAAK,WAAW;AAEhB,eAAK,YAAY,SAAS,MAAM;AAW9B,iBAAK,WAAW,KAAK;AACrB,iBAAK,YAAY;AACjB,gBAAI,CAAC,MAAM;AACT,mBAAK,YAAY;AAAA,YACnB,WAAW,OAAO,SAAS,UAAU;AACnC,mBAAK,YAAY;AAAA,YACnB,WAAW,QAAQ,QAAQ,KAAK,UAAU,cAAc,IAAI,GAAG;AAC7D,mBAAK,YAAY;AAAA,YACnB,WAAW,QAAQ,YAAY,SAAS,UAAU,cAAc,IAAI,GAAG;AACrE,mBAAK,gBAAgB;AAAA,YACvB,WAAW,QAAQ,gBAAgB,gBAAgB,UAAU,cAAc,IAAI,GAAG;AAChF,mBAAK,YAAY,KAAK,SAAS;AAAA,YACjC,WAAW,QAAQ,eAAe,QAAQ,QAAQ,WAAW,IAAI,GAAG;AAClE,mBAAK,mBAAmB,YAAY,KAAK,MAAM;AAE/C,mBAAK,YAAY,IAAI,KAAK,CAAC,KAAK,gBAAgB,CAAC;AAAA,YACnD,WAAW,QAAQ,gBAAgB,YAAY,UAAU,cAAc,IAAI,KAAK,kBAAkB,IAAI,IAAI;AACxG,mBAAK,mBAAmB,YAAY,IAAI;AAAA,YAC1C,OAAO;AACL,mBAAK,YAAY,OAAO,OAAO,UAAU,SAAS,KAAK,IAAI;AAAA,YAC7D;AAEA,gBAAI,CAAC,KAAK,QAAQ,IAAI,cAAc,GAAG;AACrC,kBAAI,OAAO,SAAS,UAAU;AAC5B,qBAAK,QAAQ,IAAI,gBAAgB,0BAA0B;AAAA,cAC7D,WAAW,KAAK,aAAa,KAAK,UAAU,MAAM;AAChD,qBAAK,QAAQ,IAAI,gBAAgB,KAAK,UAAU,IAAI;AAAA,cACtD,WAAW,QAAQ,gBAAgB,gBAAgB,UAAU,cAAc,IAAI,GAAG;AAChF,qBAAK,QAAQ,IAAI,gBAAgB,iDAAiD;AAAA,cACpF;AAAA,YACF;AAAA,UACF;AAEA,cAAI,QAAQ,MAAM;AAChB,iBAAK,OAAO,WAAW;AACrB,kBAAI,WAAW,SAAS,IAAI;AAC5B,kBAAI,UAAU;AACZ,uBAAO;AAAA,cACT;AAEA,kBAAI,KAAK,WAAW;AAClB,uBAAO,QAAQ,QAAQ,KAAK,SAAS;AAAA,cACvC,WAAW,KAAK,kBAAkB;AAChC,uBAAO,QAAQ,QAAQ,IAAI,KAAK,CAAC,KAAK,gBAAgB,CAAC,CAAC;AAAA,cAC1D,WAAW,KAAK,eAAe;AAC7B,sBAAM,IAAI,MAAM,sCAAsC;AAAA,cACxD,OAAO;AACL,uBAAO,QAAQ,QAAQ,IAAI,KAAK,CAAC,KAAK,SAAS,CAAC,CAAC;AAAA,cACnD;AAAA,YACF;AAEA,iBAAK,cAAc,WAAW;AAC5B,kBAAI,KAAK,kBAAkB;AACzB,oBAAI,aAAa,SAAS,IAAI;AAC9B,oBAAI,YAAY;AACd,yBAAO;AAAA,gBACT;AACA,oBAAI,YAAY,OAAO,KAAK,gBAAgB,GAAG;AAC7C,yBAAO,QAAQ;AAAA,oBACb,KAAK,iBAAiB,OAAO;AAAA,sBAC3B,KAAK,iBAAiB;AAAA,sBACtB,KAAK,iBAAiB,aAAa,KAAK,iBAAiB;AAAA,oBAC3D;AAAA,kBACF;AAAA,gBACF,OAAO;AACL,yBAAO,QAAQ,QAAQ,KAAK,gBAAgB;AAAA,gBAC9C;AAAA,cACF,OAAO;AACL,uBAAO,KAAK,KAAK,EAAE,KAAK,qBAAqB;AAAA,cAC/C;AAAA,YACF;AAAA,UACF;AAEA,eAAK,OAAO,WAAW;AACrB,gBAAI,WAAW,SAAS,IAAI;AAC5B,gBAAI,UAAU;AACZ,qBAAO;AAAA,YACT;AAEA,gBAAI,KAAK,WAAW;AAClB,qBAAO,eAAe,KAAK,SAAS;AAAA,YACtC,WAAW,KAAK,kBAAkB;AAChC,qBAAO,QAAQ,QAAQ,sBAAsB,KAAK,gBAAgB,CAAC;AAAA,YACrE,WAAW,KAAK,eAAe;AAC7B,oBAAM,IAAI,MAAM,sCAAsC;AAAA,YACxD,OAAO;AACL,qBAAO,QAAQ,QAAQ,KAAK,SAAS;AAAA,YACvC;AAAA,UACF;AAEA,cAAI,QAAQ,UAAU;AACpB,iBAAK,WAAW,WAAW;AACzB,qBAAO,KAAK,KAAK,EAAE,KAAK,MAAM;AAAA,YAChC;AAAA,UACF;AAEA,eAAK,OAAO,WAAW;AACrB,mBAAO,KAAK,KAAK,EAAE,KAAK,KAAK,KAAK;AAAA,UACpC;AAEA,iBAAO;AAAA,QACT;AAGA,YAAI,UAAU,CAAC,UAAU,OAAO,QAAQ,WAAW,QAAQ,KAAK;AAEhE,iBAAS,gBAAgB,QAAQ;AAC/B,cAAI,UAAU,OAAO,YAAY;AACjC,iBAAO,QAAQ,QAAQ,OAAO,IAAI,KAAK,UAAU;AAAA,QACnD;AAEA,iBAAS,QAAQ,OAAO,SAAS;AAC/B,cAAI,EAAE,gBAAgB,UAAU;AAC9B,kBAAM,IAAI,UAAU,4FAA4F;AAAA,UAClH;AAEA,oBAAU,WAAW,CAAC;AACtB,cAAI,OAAO,QAAQ;AAEnB,cAAI,iBAAiB,SAAS;AAC5B,gBAAI,MAAM,UAAU;AAClB,oBAAM,IAAI,UAAU,cAAc;AAAA,YACpC;AACA,iBAAK,MAAM,MAAM;AACjB,iBAAK,cAAc,MAAM;AACzB,gBAAI,CAAC,QAAQ,SAAS;AACpB,mBAAK,UAAU,IAAI,QAAQ,MAAM,OAAO;AAAA,YAC1C;AACA,iBAAK,SAAS,MAAM;AACpB,iBAAK,OAAO,MAAM;AAClB,iBAAK,SAAS,MAAM;AACpB,gBAAI,CAAC,QAAQ,MAAM,aAAa,MAAM;AACpC,qBAAO,MAAM;AACb,oBAAM,WAAW;AAAA,YACnB;AAAA,UACF,OAAO;AACL,iBAAK,MAAM,OAAO,KAAK;AAAA,UACzB;AAEA,eAAK,cAAc,QAAQ,eAAe,KAAK,eAAe;AAC9D,cAAI,QAAQ,WAAW,CAAC,KAAK,SAAS;AACpC,iBAAK,UAAU,IAAI,QAAQ,QAAQ,OAAO;AAAA,UAC5C;AACA,eAAK,SAAS,gBAAgB,QAAQ,UAAU,KAAK,UAAU,KAAK;AACpE,eAAK,OAAO,QAAQ,QAAQ,KAAK,QAAQ;AACzC,eAAK,SAAS,QAAQ,UAAU,KAAK;AACrC,eAAK,WAAW;AAEhB,eAAK,KAAK,WAAW,SAAS,KAAK,WAAW,WAAW,MAAM;AAC7D,kBAAM,IAAI,UAAU,2CAA2C;AAAA,UACjE;AACA,eAAK,UAAU,IAAI;AAEnB,cAAI,KAAK,WAAW,SAAS,KAAK,WAAW,QAAQ;AACnD,gBAAI,QAAQ,UAAU,cAAc,QAAQ,UAAU,YAAY;AAEhE,kBAAI,gBAAgB;AACpB,kBAAI,cAAc,KAAK,KAAK,GAAG,GAAG;AAEhC,qBAAK,MAAM,KAAK,IAAI,QAAQ,eAAe,UAAS,oBAAI,KAAK,GAAE,QAAQ,CAAC;AAAA,cAC1E,OAAO;AAEL,oBAAI,gBAAgB;AACpB,qBAAK,QAAQ,cAAc,KAAK,KAAK,GAAG,IAAI,MAAM,OAAO,QAAO,oBAAI,KAAK,GAAE,QAAQ;AAAA,cACrF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,gBAAQ,UAAU,QAAQ,WAAW;AACnC,iBAAO,IAAI,QAAQ,MAAM,EAAC,MAAM,KAAK,UAAS,CAAC;AAAA,QACjD;AAEA,iBAAS,OAAO,MAAM;AACpB,cAAI,OAAO,IAAI,SAAS;AACxB,eACG,KAAK,EACL,MAAM,GAAG,EACT,QAAQ,SAAS,OAAO;AACvB,gBAAI,OAAO;AACT,kBAAI,QAAQ,MAAM,MAAM,GAAG;AAC3B,kBAAI,OAAO,MAAM,MAAM,EAAE,QAAQ,OAAO,GAAG;AAC3C,kBAAI,QAAQ,MAAM,KAAK,GAAG,EAAE,QAAQ,OAAO,GAAG;AAC9C,mBAAK,OAAO,mBAAmB,IAAI,GAAG,mBAAmB,KAAK,CAAC;AAAA,YACjE;AAAA,UACF,CAAC;AACH,iBAAO;AAAA,QACT;AAEA,iBAAS,aAAa,YAAY;AAChC,cAAI,UAAU,IAAI,QAAQ;AAG1B,cAAI,sBAAsB,WAAW,QAAQ,gBAAgB,GAAG;AAIhE,8BACG,MAAM,IAAI,EACV,IAAI,SAAS,QAAQ;AACpB,mBAAO,OAAO,QAAQ,IAAI,MAAM,IAAI,OAAO,OAAO,GAAG,OAAO,MAAM,IAAI;AAAA,UACxE,CAAC,EACA,QAAQ,SAAS,MAAM;AACtB,gBAAI,QAAQ,KAAK,MAAM,GAAG;AAC1B,gBAAI,MAAM,MAAM,MAAM,EAAE,KAAK;AAC7B,gBAAI,KAAK;AACP,kBAAI,QAAQ,MAAM,KAAK,GAAG,EAAE,KAAK;AACjC,sBAAQ,OAAO,KAAK,KAAK;AAAA,YAC3B;AAAA,UACF,CAAC;AACH,iBAAO;AAAA,QACT;AAEA,aAAK,KAAK,QAAQ,SAAS;AAE3B,iBAAS,SAAS,UAAU,SAAS;AACnC,cAAI,EAAE,gBAAgB,WAAW;AAC/B,kBAAM,IAAI,UAAU,4FAA4F;AAAA,UAClH;AACA,cAAI,CAAC,SAAS;AACZ,sBAAU,CAAC;AAAA,UACb;AAEA,eAAK,OAAO;AACZ,eAAK,SAAS,QAAQ,WAAW,SAAY,MAAM,QAAQ;AAC3D,eAAK,KAAK,KAAK,UAAU,OAAO,KAAK,SAAS;AAC9C,eAAK,aAAa,QAAQ,eAAe,SAAY,KAAK,KAAK,QAAQ;AACvE,eAAK,UAAU,IAAI,QAAQ,QAAQ,OAAO;AAC1C,eAAK,MAAM,QAAQ,OAAO;AAC1B,eAAK,UAAU,QAAQ;AAAA,QACzB;AAEA,aAAK,KAAK,SAAS,SAAS;AAE5B,iBAAS,UAAU,QAAQ,WAAW;AACpC,iBAAO,IAAI,SAAS,KAAK,WAAW;AAAA,YAClC,QAAQ,KAAK;AAAA,YACb,YAAY,KAAK;AAAA,YACjB,SAAS,IAAI,QAAQ,KAAK,OAAO;AAAA,YACjC,KAAK,KAAK;AAAA,UACZ,CAAC;AAAA,QACH;AAEA,iBAAS,QAAQ,WAAW;AAC1B,cAAI,WAAW,IAAI,SAAS,MAAM,EAAC,QAAQ,GAAG,YAAY,GAAE,CAAC;AAC7D,mBAAS,OAAO;AAChB,iBAAO;AAAA,QACT;AAEA,YAAI,mBAAmB,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;AAE/C,iBAAS,WAAW,SAAS,KAAK,QAAQ;AACxC,cAAI,iBAAiB,QAAQ,MAAM,MAAM,IAAI;AAC3C,kBAAM,IAAI,WAAW,qBAAqB;AAAA,UAC5C;AAEA,iBAAO,IAAI,SAAS,MAAM,EAAC,QAAgB,SAAS,EAAC,UAAU,IAAG,EAAC,CAAC;AAAA,QACtE;AAEA,QAAAD,SAAQ,eAAeC,QAAO;AAC9B,YAAI;AACF,cAAID,SAAQ,aAAa;AAAA,QAC3B,SAAS,KAAK;AACZ,UAAAA,SAAQ,eAAe,SAAS,SAAS,MAAM;AAC7C,iBAAK,UAAU;AACf,iBAAK,OAAO;AACZ,gBAAI,QAAQ,MAAM,OAAO;AACzB,iBAAK,QAAQ,MAAM;AAAA,UACrB;AACA,UAAAA,SAAQ,aAAa,YAAY,OAAO,OAAO,MAAM,SAAS;AAC9D,UAAAA,SAAQ,aAAa,UAAU,cAAcA,SAAQ;AAAA,QACvD;AAEA,iBAASE,OAAM,OAAO,MAAM;AAC1B,iBAAO,IAAI,QAAQ,SAAS,SAAS,QAAQ;AAC3C,gBAAIC,WAAU,IAAI,QAAQ,OAAO,IAAI;AAErC,gBAAIA,SAAQ,UAAUA,SAAQ,OAAO,SAAS;AAC5C,qBAAO,OAAO,IAAIH,SAAQ,aAAa,WAAW,YAAY,CAAC;AAAA,YACjE;AAEA,gBAAI,MAAM,IAAI,eAAe;AAE7B,qBAAS,WAAW;AAClB,kBAAI,MAAM;AAAA,YACZ;AAEA,gBAAI,SAAS,WAAW;AACtB,kBAAI,UAAU;AAAA,gBACZ,QAAQ,IAAI;AAAA,gBACZ,YAAY,IAAI;AAAA,gBAChB,SAAS,aAAa,IAAI,sBAAsB,KAAK,EAAE;AAAA,cACzD;AACA,sBAAQ,MAAM,iBAAiB,MAAM,IAAI,cAAc,QAAQ,QAAQ,IAAI,eAAe;AAC1F,kBAAI,OAAO,cAAc,MAAM,IAAI,WAAW,IAAI;AAClD,yBAAW,WAAW;AACpB,wBAAQ,IAAI,SAAS,MAAM,OAAO,CAAC;AAAA,cACrC,GAAG,CAAC;AAAA,YACN;AAEA,gBAAI,UAAU,WAAW;AACvB,yBAAW,WAAW;AACpB,uBAAO,IAAI,UAAU,wBAAwB,CAAC;AAAA,cAChD,GAAG,CAAC;AAAA,YACN;AAEA,gBAAI,YAAY,WAAW;AACzB,yBAAW,WAAW;AACpB,uBAAO,IAAI,UAAU,wBAAwB,CAAC;AAAA,cAChD,GAAG,CAAC;AAAA,YACN;AAEA,gBAAI,UAAU,WAAW;AACvB,yBAAW,WAAW;AACpB,uBAAO,IAAIA,SAAQ,aAAa,WAAW,YAAY,CAAC;AAAA,cAC1D,GAAG,CAAC;AAAA,YACN;AAEA,qBAAS,OAAO,KAAK;AACnB,kBAAI;AACF,uBAAO,QAAQ,MAAMC,QAAO,SAAS,OAAOA,QAAO,SAAS,OAAO;AAAA,cACrE,SAAS,GAAG;AACV,uBAAO;AAAA,cACT;AAAA,YACF;AAEA,gBAAI,KAAKE,SAAQ,QAAQ,OAAOA,SAAQ,GAAG,GAAG,IAAI;AAElD,gBAAIA,SAAQ,gBAAgB,WAAW;AACrC,kBAAI,kBAAkB;AAAA,YACxB,WAAWA,SAAQ,gBAAgB,QAAQ;AACzC,kBAAI,kBAAkB;AAAA,YACxB;AAEA,gBAAI,kBAAkB,KAAK;AACzB,kBAAI,QAAQ,MAAM;AAChB,oBAAI,eAAe;AAAA,cACrB,WACE,QAAQ,eACRA,SAAQ,QAAQ,IAAI,cAAc,KAClCA,SAAQ,QAAQ,IAAI,cAAc,EAAE,QAAQ,0BAA0B,MAAM,IAC5E;AACA,oBAAI,eAAe;AAAA,cACrB;AAAA,YACF;AAEA,gBAAI,QAAQ,OAAO,KAAK,YAAY,YAAY,EAAE,KAAK,mBAAmB,UAAU;AAClF,qBAAO,oBAAoB,KAAK,OAAO,EAAE,QAAQ,SAAS,MAAM;AAC9D,oBAAI,iBAAiB,MAAM,eAAe,KAAK,QAAQ,IAAI,CAAC,CAAC;AAAA,cAC/D,CAAC;AAAA,YACH,OAAO;AACL,cAAAA,SAAQ,QAAQ,QAAQ,SAAS,OAAO,MAAM;AAC5C,oBAAI,iBAAiB,MAAM,KAAK;AAAA,cAClC,CAAC;AAAA,YACH;AAEA,gBAAIA,SAAQ,QAAQ;AAClB,cAAAA,SAAQ,OAAO,iBAAiB,SAAS,QAAQ;AAEjD,kBAAI,qBAAqB,WAAW;AAElC,oBAAI,IAAI,eAAe,GAAG;AACxB,kBAAAA,SAAQ,OAAO,oBAAoB,SAAS,QAAQ;AAAA,gBACtD;AAAA,cACF;AAAA,YACF;AAEA,gBAAI,KAAK,OAAOA,SAAQ,cAAc,cAAc,OAAOA,SAAQ,SAAS;AAAA,UAC9E,CAAC;AAAA,QACH;AAEA,QAAAD,OAAM,WAAW;AAEjB,YAAI,CAACD,QAAO,OAAO;AACjB,UAAAA,QAAO,QAAQC;AACf,UAAAD,QAAO,UAAU;AACjB,UAAAA,QAAO,UAAU;AACjB,UAAAA,QAAO,WAAW;AAAA,QACpB;AAEA,QAAAD,SAAQ,UAAU;AAClB,QAAAA,SAAQ,UAAU;AAClB,QAAAA,SAAQ,WAAW;AACnB,QAAAA,SAAQ,QAAQE;AAEhB,eAAOF;AAAA,MAET,EAAG,CAAC,CAAC;AAAA,IACL,GAAG,cAAc;AAEjB,mBAAe,MAAM,WAAW;AAChC,WAAO,eAAe,MAAM;AAE5B,QAAI,MAAM,WAAW,QAAQ,aAAa;AAC1C,cAAU,IAAI;AACd,YAAQ,UAAU,IAAI;AACtB,YAAQ,QAAQ,IAAI;AACpB,YAAQ,UAAU,IAAI;AACtB,YAAQ,UAAU,IAAI;AACtB,YAAQ,WAAW,IAAI;AACvB,WAAO,UAAU;AAAA;AAAA;;;ACtoBjB;AAAA;AAAA,QAAII,YAAW,OAAO,UAAU,aAAa,QAAQ;AACrD,QAAI,OAAO,WAAW,eAAe,OAAO,OAAO;AACjD,MAAAA,YAAW,OAAO;AAAA,IACpB,WAAW,OAAO,WAAW,eAAe,OAAO,OAAO;AACxD,MAAAA,YAAW,OAAO;AAAA,IACpB;AAEA,QAAI,OAAO,cAAY,eAAe,OAAO,WAAW,aAAa;AAC/D,UAAIA,aAAY;AACpB,UAAI,EAAE,QAAS,KAAI,EAAE;AACrB,cAAQ,UAAU;AAClB,aAAO,UAAU,QAAQ;AAAA,IAC3B;AAJM;AAAA;AAAA;;;ACRN,SAAS,QAAQ,GAAG;AAAE;AAA2B,SAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,IAAG;AAAE,WAAO,OAAOA;AAAA,EAAG,IAAI,SAAUA,IAAG;AAAE,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EAAG,GAAG,QAAQ,CAAC;AAAG;AAC7T,IAAI,MAAM,CAAC;AACX,IAAI,OAAO,IAAI;AACf,IAAI,QAAQ,IAAI;AAWT,SAAS,oBAAoB;AAClC,SAAO,OAAO,mBAAmB,eAAe,OAAO,mBAAmB,cAAc,cAAc,QAAQ,cAAc,OAAO;AACrI;AACA,SAAS,UAAU,cAAc;AAC/B,SAAO,CAAC,CAAC,gBAAgB,OAAO,aAAa,SAAS;AACxD;AACO,SAAS,YAAY,cAAc;AACxC,MAAI,UAAU,YAAY,GAAG;AAC3B,WAAO;AAAA,EACT;AACA,SAAO,QAAQ,QAAQ,YAAY;AACrC;;;AClBA,gBAA2B;AAP3B,SAAS,QAAQ,GAAG,GAAG;AAAE,MAAI,IAAI,OAAO,KAAK,CAAC;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,IAAI,OAAO,sBAAsB,CAAC;AAAG,UAAM,IAAI,EAAE,OAAO,SAAUC,IAAG;AAAE,aAAO,OAAO,yBAAyB,GAAGA,EAAC,EAAE;AAAA,IAAY,CAAC,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,EAAG;AAAE,SAAO;AAAG;AAC9P,SAAS,cAAc,GAAG;AAAE,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,QAAI,IAAI,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AAAG,QAAI,IAAI,QAAQ,OAAO,CAAC,GAAG,IAAE,EAAE,QAAQ,SAAUA,IAAG;AAAE,sBAAgB,GAAGA,IAAG,EAAEA,EAAC,CAAC;AAAA,IAAG,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,CAAC,CAAC,IAAI,QAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAUA,IAAG;AAAE,aAAO,eAAe,GAAGA,IAAG,OAAO,yBAAyB,GAAGA,EAAC,CAAC;AAAA,IAAG,CAAC;AAAA,EAAG;AAAE,SAAO;AAAG;AACtb,SAAS,gBAAgB,GAAG,GAAG,GAAG;AAAE,UAAQ,IAAI,eAAe,CAAC,MAAM,IAAI,OAAO,eAAe,GAAG,GAAG,EAAE,OAAO,GAAG,YAAY,MAAI,cAAc,MAAI,UAAU,KAAG,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AAAG;AACnL,SAAS,eAAe,GAAG;AAAE,MAAI,IAAI,aAAa,GAAG,QAAQ;AAAG,SAAO,YAAYC,SAAQ,CAAC,IAAI,IAAI,IAAI;AAAI;AAC5G,SAAS,aAAa,GAAG,GAAG;AAAE,MAAI,YAAYA,SAAQ,CAAC,KAAK,CAAC,EAAG,QAAO;AAAG,MAAI,IAAI,EAAE,OAAO,WAAW;AAAG,MAAI,WAAW,GAAG;AAAE,QAAI,IAAI,EAAE,KAAK,GAAG,KAAK,SAAS;AAAG,QAAI,YAAYA,SAAQ,CAAC,EAAG,QAAO;AAAG,UAAM,IAAI,UAAU,8CAA8C;AAAA,EAAG;AAAE,UAAQ,aAAa,IAAI,SAAS,QAAQ,CAAC;AAAG;AAC3T,SAASA,SAAQ,GAAG;AAAE;AAA2B,SAAOA,WAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,IAAG;AAAE,WAAO,OAAOA;AAAA,EAAG,IAAI,SAAUA,IAAG;AAAE,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EAAG,GAAGD,SAAQ,CAAC;AAAG;AAG7T,IAAI,WAAW,OAAO,UAAU,aAAa,QAAQ;AACrD,IAAI,OAAO,WAAW,eAAe,OAAO,OAAO;AACjD,aAAW,OAAO;AACpB,WAAW,OAAO,WAAW,eAAe,OAAO,OAAO;AACxD,aAAW,OAAO;AACpB;AACA,IAAI;AACJ,IAAI,kBAAkB,GAAG;AACvB,MAAI,OAAO,WAAW,eAAe,OAAO,gBAAgB;AAC1D,wBAAoB,OAAO;AAAA,EAC7B,WAAW,OAAO,WAAW,eAAe,OAAO,gBAAgB;AACjE,wBAAoB,OAAO;AAAA,EAC7B;AACF;AACA,IAAI;AACJ,IAAI,OAAO,kBAAkB,YAAY;AACvC,MAAI,OAAO,WAAW,eAAe,OAAO,eAAe;AACzD,uBAAmB,OAAO;AAAA,EAC5B,WAAW,OAAO,WAAW,eAAe,OAAO,eAAe;AAChE,uBAAmB,OAAO;AAAA,EAC5B;AACF;AACA,IAAI,CAAC,YAAY,aAAa,CAAC,qBAAqB,CAAC,iBAAkB,YAAqB,qBAAW;AACvG,IAAI,OAAO,aAAa,WAAY,YAAW;AAC/C,IAAI,iBAAiB,SAASE,gBAAe,KAAK,QAAQ;AACxD,MAAI,UAAUF,SAAQ,MAAM,MAAM,UAAU;AAC1C,QAAI,cAAc;AAClB,aAAS,aAAa,QAAQ;AAC5B,qBAAe,MAAM,mBAAmB,SAAS,IAAI,MAAM,mBAAmB,OAAO,SAAS,CAAC;AAAA,IACjG;AACA,QAAI,CAAC,YAAa,QAAO;AACzB,UAAM,OAAO,IAAI,QAAQ,GAAG,MAAM,KAAK,MAAM,OAAO,YAAY,MAAM,CAAC;AAAA,EACzE;AACA,SAAO;AACT;AACA,IAAI,UAAU,SAASG,SAAQ,KAAK,cAAc,UAAU,UAAU;AACpE,MAAI,WAAW,SAASC,UAAS,UAAU;AACzC,QAAI,CAAC,SAAS,GAAI,QAAO,SAAS,SAAS,cAAc,SAAS;AAAA,MAChE,QAAQ,SAAS;AAAA,IACnB,CAAC;AACD,aAAS,KAAK,EAAE,KAAK,SAAU,MAAM;AACnC,eAAS,MAAM;AAAA,QACb,QAAQ,SAAS;AAAA,QACjB;AAAA,MACF,CAAC;AAAA,IACH,CAAC,EAAE,MAAM,QAAQ;AAAA,EACnB;AACA,MAAI,UAAU;AACZ,QAAI,cAAc,SAAS,KAAK,YAAY;AAC5C,QAAI,uBAAuB,SAAS;AAClC,kBAAY,KAAK,QAAQ,EAAE,MAAM,QAAQ;AACzC;AAAA,IACF;AAAA,EACF;AACA,MAAI,OAAO,UAAU,YAAY;AAC/B,UAAM,KAAK,YAAY,EAAE,KAAK,QAAQ,EAAE,MAAM,QAAQ;AAAA,EACxD,OAAO;AACL,aAAS,KAAK,YAAY,EAAE,KAAK,QAAQ,EAAE,MAAM,QAAQ;AAAA,EAC3D;AACF;AACA,IAAI,mBAAmB;AACvB,IAAI,mBAAmB,SAASC,kBAAiB,SAAS,KAAK,SAAS,UAAU;AAChF,MAAI,QAAQ,mBAAmB;AAC7B,UAAM,eAAe,KAAK,QAAQ,iBAAiB;AAAA,EACrD;AACA,MAAI,UAAU,cAAc,CAAC,GAAG,OAAO,QAAQ,kBAAkB,aAAa,QAAQ,cAAc,IAAI,QAAQ,aAAa;AAC7H,MAAI,OAAO,WAAW,eAAe,OAAO,WAAW,eAAe,OAAO,OAAO,YAAY,eAAe,OAAO,QAAQ,YAAY,OAAO,QAAQ,SAAS,MAAM;AACtK,YAAQ,YAAY,IAAI,8BAA8B,OAAO,OAAO,QAAQ,SAAS,IAAI,EAAE,OAAO,OAAO,QAAQ,UAAU,GAAG,EAAE,OAAO,OAAO,QAAQ,MAAM,GAAG;AAAA,EACjK;AACA,MAAI,QAAS,SAAQ,cAAc,IAAI;AACvC,MAAI,aAAa,OAAO,QAAQ,mBAAmB,aAAa,QAAQ,eAAe,OAAO,IAAI,QAAQ;AAC1G,MAAI,eAAe,cAAc;AAAA,IAC/B,QAAQ,UAAU,SAAS;AAAA,IAC3B,MAAM,UAAU,QAAQ,UAAU,OAAO,IAAI;AAAA,IAC7C;AAAA,EACF,GAAG,mBAAmB,CAAC,IAAI,UAAU;AACrC,MAAI,WAAW,OAAO,QAAQ,mBAAmB,cAAc,QAAQ,eAAe,UAAU,IAAI,QAAQ,iBAAiB;AAC7H,MAAI;AACF,YAAQ,KAAK,cAAc,UAAU,QAAQ;AAAA,EAC/C,SAAS,GAAG;AACV,QAAI,CAAC,cAAc,OAAO,KAAK,UAAU,EAAE,WAAW,KAAK,CAAC,EAAE,WAAW,EAAE,QAAQ,QAAQ,iBAAiB,IAAI,GAAG;AACjH,aAAO,SAAS,CAAC;AAAA,IACnB;AACA,QAAI;AACF,aAAO,KAAK,UAAU,EAAE,QAAQ,SAAU,KAAK;AAC7C,eAAO,aAAa,GAAG;AAAA,MACzB,CAAC;AACD,cAAQ,KAAK,cAAc,UAAU,QAAQ;AAC7C,yBAAmB;AAAA,IACrB,SAAS,KAAK;AACZ,eAAS,GAAG;AAAA,IACd;AAAA,EACF;AACF;AACA,IAAI,4BAA4B,SAASC,2BAA0B,SAAS,KAAK,SAAS,UAAU;AAClG,MAAI,WAAWN,SAAQ,OAAO,MAAM,UAAU;AAC5C,cAAU,eAAe,IAAI,OAAO,EAAE,MAAM,CAAC;AAAA,EAC/C;AACA,MAAI,QAAQ,mBAAmB;AAC7B,UAAM,eAAe,KAAK,QAAQ,iBAAiB;AAAA,EACrD;AACA,MAAI;AACF,QAAI;AACJ,QAAI,mBAAmB;AACrB,UAAI,IAAI,kBAAkB;AAAA,IAC5B,OAAO;AACL,UAAI,IAAI,iBAAiB,oBAAoB;AAAA,IAC/C;AACA,MAAE,KAAK,UAAU,SAAS,OAAO,KAAK,CAAC;AACvC,QAAI,CAAC,QAAQ,aAAa;AACxB,QAAE,iBAAiB,oBAAoB,gBAAgB;AAAA,IACzD;AACA,MAAE,kBAAkB,CAAC,CAAC,QAAQ;AAC9B,QAAI,SAAS;AACX,QAAE,iBAAiB,gBAAgB,mCAAmC;AAAA,IACxE;AACA,QAAI,EAAE,kBAAkB;AACtB,QAAE,iBAAiB,kBAAkB;AAAA,IACvC;AACA,QAAI,IAAI,QAAQ;AAChB,QAAI,OAAO,MAAM,aAAa,EAAE,IAAI;AACpC,QAAI,GAAG;AACL,eAAS,KAAK,GAAG;AACf,UAAE,iBAAiB,GAAG,EAAE,CAAC,CAAC;AAAA,MAC5B;AAAA,IACF;AACA,MAAE,qBAAqB,WAAY;AACjC,QAAE,aAAa,KAAK,SAAS,EAAE,UAAU,MAAM,EAAE,aAAa,MAAM;AAAA,QAClE,QAAQ,EAAE;AAAA,QACV,MAAM,EAAE;AAAA,MACV,CAAC;AAAA,IACH;AACA,MAAE,KAAK,OAAO;AAAA,EAChB,SAAS,GAAG;AACV,eAAW,QAAQ,IAAI,CAAC;AAAA,EAC1B;AACF;AACA,IAAI,UAAU,SAASO,SAAQ,SAAS,KAAK,SAAS,UAAU;AAC9D,MAAI,OAAO,YAAY,YAAY;AACjC,eAAW;AACX,cAAU;AAAA,EACZ;AACA,aAAW,YAAY,WAAY;AAAA,EAAC;AACpC,MAAI,YAAY,IAAI,QAAQ,OAAO,MAAM,GAAG;AAC1C,WAAO,iBAAiB,SAAS,KAAK,SAAS,QAAQ;AAAA,EACzD;AACA,MAAI,kBAAkB,KAAK,OAAO,kBAAkB,YAAY;AAC9D,WAAO,0BAA0B,SAAS,KAAK,SAAS,QAAQ;AAAA,EAClE;AACA,WAAS,IAAI,MAAM,2CAA2C,CAAC;AACjE;AACA,IAAO,kBAAQ;;;AC/Jf,SAASC,SAAQ,GAAG;AAAE;AAA2B,SAAOA,WAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,IAAG;AAAE,WAAO,OAAOA;AAAA,EAAG,IAAI,SAAUA,IAAG;AAAE,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EAAG,GAAGD,SAAQ,CAAC;AAAG;AAC7T,SAASE,SAAQ,GAAG,GAAG;AAAE,MAAI,IAAI,OAAO,KAAK,CAAC;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,IAAI,OAAO,sBAAsB,CAAC;AAAG,UAAM,IAAI,EAAE,OAAO,SAAUC,IAAG;AAAE,aAAO,OAAO,yBAAyB,GAAGA,EAAC,EAAE;AAAA,IAAY,CAAC,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;AAAA,EAAG;AAAE,SAAO;AAAG;AAC9P,SAASC,eAAc,GAAG;AAAE,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,QAAI,IAAI,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AAAG,QAAI,IAAIF,SAAQ,OAAO,CAAC,GAAG,IAAE,EAAE,QAAQ,SAAUC,IAAG;AAAE,MAAAE,iBAAgB,GAAGF,IAAG,EAAEA,EAAC,CAAC;AAAA,IAAG,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0B,CAAC,CAAC,IAAID,SAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAUC,IAAG;AAAE,aAAO,eAAe,GAAGA,IAAG,OAAO,yBAAyB,GAAGA,EAAC,CAAC;AAAA,IAAG,CAAC;AAAA,EAAG;AAAE,SAAO;AAAG;AACtb,SAAS,gBAAgB,GAAG,GAAG;AAAE,MAAI,EAAE,aAAa,GAAI,OAAM,IAAI,UAAU,mCAAmC;AAAG;AAClH,SAAS,kBAAkB,GAAG,GAAG;AAAE,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AAAE,QAAI,IAAI,EAAE,CAAC;AAAG,MAAE,aAAa,EAAE,cAAc,OAAI,EAAE,eAAe,MAAI,WAAW,MAAM,EAAE,WAAW,OAAK,OAAO,eAAe,GAAGG,gBAAe,EAAE,GAAG,GAAG,CAAC;AAAA,EAAG;AAAE;AACvO,SAAS,aAAa,GAAG,GAAG,GAAG;AAAE,SAAO,KAAK,kBAAkB,EAAE,WAAW,CAAC,GAAG,KAAK,kBAAkB,GAAG,CAAC,GAAG,OAAO,eAAe,GAAG,aAAa,EAAE,UAAU,MAAG,CAAC,GAAG;AAAG;AAC1K,SAASD,iBAAgB,GAAG,GAAG,GAAG;AAAE,UAAQ,IAAIC,gBAAe,CAAC,MAAM,IAAI,OAAO,eAAe,GAAG,GAAG,EAAE,OAAO,GAAG,YAAY,MAAI,cAAc,MAAI,UAAU,KAAG,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AAAG;AACnL,SAASA,gBAAe,GAAG;AAAE,MAAI,IAAIC,cAAa,GAAG,QAAQ;AAAG,SAAO,YAAYP,SAAQ,CAAC,IAAI,IAAI,IAAI;AAAI;AAC5G,SAASO,cAAa,GAAG,GAAG;AAAE,MAAI,YAAYP,SAAQ,CAAC,KAAK,CAAC,EAAG,QAAO;AAAG,MAAI,IAAI,EAAE,OAAO,WAAW;AAAG,MAAI,WAAW,GAAG;AAAE,QAAI,IAAI,EAAE,KAAK,GAAG,KAAK,SAAS;AAAG,QAAI,YAAYA,SAAQ,CAAC,EAAG,QAAO;AAAG,UAAM,IAAI,UAAU,8CAA8C;AAAA,EAAG;AAAE,UAAQ,aAAa,IAAI,SAAS,QAAQ,CAAC;AAAG;AAG3T,IAAI,cAAc,SAASQ,eAAc;AACvC,SAAO;AAAA,IACL,UAAU;AAAA,IACV,SAAS;AAAA,IACT,OAAO,SAAS,MAAM,MAAM;AAC1B,aAAO,KAAK,MAAM,IAAI;AAAA,IACxB;AAAA,IACA,WAAW,KAAK;AAAA,IAChB,cAAc,SAAS,aAAa,WAAW,KAAK,eAAe;AACjE,aAAOH,iBAAgB,CAAC,GAAG,KAAK,iBAAiB,EAAE;AAAA,IACrD;AAAA,IACA,kBAAkB,SAAS,iBAAiB,WAAW,YAAY;AACjE,aAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,IACT,gBAAgB,OAAO,WAAW,cAAc,QAAQ,KAAK,KAAK;AAAA,IAClE,eAAe,CAAC;AAAA,IAChB,mBAAmB,CAAC;AAAA,IACpB,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,MACd,MAAM;AAAA,MACN,aAAa;AAAA,MACb,OAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,IAAI,UAAU,WAAY;AACxB,WAASI,SAAQ,UAAU;AACzB,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,QAAI,aAAa,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACtF,oBAAgB,MAAMA,QAAO;AAC7B,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,OAAO;AACZ,SAAK,KAAK,UAAU,SAAS,UAAU;AAAA,EACzC;AACA,SAAO,aAAaA,UAAS,CAAC;AAAA,IAC5B,KAAK;AAAA,IACL,OAAO,SAAS,KAAK,UAAU;AAC7B,UAAI,QAAQ;AACZ,UAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,UAAI,aAAa,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACtF,WAAK,WAAW;AAChB,WAAK,UAAUL,eAAcA,eAAcA,eAAc,CAAC,GAAG,YAAY,CAAC,GAAG,KAAK,WAAW,CAAC,CAAC,GAAG,OAAO;AACzG,WAAK,aAAa;AAClB,UAAI,KAAK,YAAY,KAAK,QAAQ,gBAAgB;AAChD,YAAI,QAAQ,YAAY,WAAY;AAClC,iBAAO,MAAM,OAAO;AAAA,QACtB,GAAG,KAAK,QAAQ,cAAc;AAC9B,YAAIJ,SAAQ,KAAK,MAAM,YAAY,OAAO,MAAM,UAAU,WAAY,OAAM,MAAM;AAAA,MACpF;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU,WAAW,YAAY,UAAU;AACzD,WAAK,SAAS,WAAW,WAAW,YAAY,YAAY,QAAQ;AAAA,IACtE;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,KAAK,UAAU,WAAW,UAAU;AAClD,WAAK,SAAS,CAAC,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG,WAAW,QAAQ;AAAA,IACtE;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS,WAAW,kBAAkB,YAAY,mBAAmB,UAAU;AAC7F,UAAI,SAAS;AACb,UAAI,WAAW,KAAK,QAAQ;AAC5B,UAAI,OAAO,KAAK,QAAQ,aAAa,YAAY;AAC/C,mBAAW,KAAK,QAAQ,SAAS,WAAW,UAAU;AAAA,MACxD;AACA,iBAAW,YAAY,QAAQ;AAC/B,eAAS,KAAK,SAAU,kBAAkB;AACxC,YAAI,CAAC,iBAAkB,QAAO,SAAS,MAAM,CAAC,CAAC;AAC/C,YAAI,MAAM,OAAO,SAAS,aAAa,YAAY,kBAAkB;AAAA,UACnE,KAAK,UAAU,KAAK,GAAG;AAAA,UACvB,IAAI,WAAW,KAAK,GAAG;AAAA,QACzB,CAAC;AACD,eAAO,QAAQ,KAAK,UAAU,kBAAkB,iBAAiB;AAAA,MACnE,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,QAAQ,KAAK,UAAU,WAAW,YAAY;AAC5D,UAAI,SAAS;AACb,UAAI,MAAM,OAAO,cAAc,WAAW,CAAC,SAAS,IAAI;AACxD,UAAI,KAAK,OAAO,eAAe,WAAW,CAAC,UAAU,IAAI;AACzD,UAAI,UAAU,KAAK,QAAQ,iBAAiB,KAAK,EAAE;AACnD,WAAK,QAAQ,QAAQ,KAAK,SAAS,KAAK,SAAS,SAAU,KAAK,KAAK;AACnE,YAAI,QAAQ,IAAI,UAAU,OAAO,IAAI,SAAS,OAAO,CAAC,IAAI,QAAS,QAAO,SAAS,oBAAoB,MAAM,oBAAoB,IAAI,QAAQ,IAAI;AACjJ,YAAI,OAAO,IAAI,UAAU,OAAO,IAAI,SAAS,IAAK,QAAO,SAAS,oBAAoB,MAAM,oBAAoB,IAAI,QAAQ,KAAK;AACjI,YAAI,CAAC,OAAO,OAAO,IAAI,SAAS;AAC9B,cAAI,eAAe,IAAI,QAAQ,YAAY;AAC3C,cAAI,iBAAiB,CAAC,UAAU,SAAS,WAAW,MAAM,EAAE,KAAK,SAAU,MAAM;AAC/E,mBAAO,aAAa,QAAQ,IAAI,IAAI;AAAA,UACtC,CAAC;AACD,cAAI,gBAAgB;AAClB,mBAAO,SAAS,oBAAoB,MAAM,OAAO,IAAI,SAAS,IAAI;AAAA,UACpE;AAAA,QACF;AACA,YAAI,IAAK,QAAO,SAAS,KAAK,KAAK;AACnC,YAAI,KAAK;AACT,YAAI;AACF,cAAI,OAAO,IAAI,SAAS,UAAU;AAChC,kBAAM,OAAO,QAAQ,MAAM,IAAI,MAAM,WAAW,UAAU;AAAA,UAC5D,OAAO;AACL,kBAAM,IAAI;AAAA,UACZ;AAAA,QACF,SAAS,GAAG;AACV,qBAAW,oBAAoB,MAAM;AAAA,QACvC;AACA,YAAI,SAAU,QAAO,SAAS,UAAU,KAAK;AAC7C,iBAAS,MAAM,GAAG;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,OAAO,WAAW,WAAW,KAAK,eAAe,UAAU;AACzE,UAAI,SAAS;AACb,UAAI,CAAC,KAAK,QAAQ,QAAS;AAC3B,UAAI,OAAO,cAAc,SAAU,aAAY,CAAC,SAAS;AACzD,UAAI,UAAU,KAAK,QAAQ,aAAa,WAAW,KAAK,aAAa;AACrE,UAAI,WAAW;AACf,UAAI,YAAY,CAAC;AACjB,UAAI,WAAW,CAAC;AAChB,gBAAU,QAAQ,SAAU,KAAK;AAC/B,YAAI,UAAU,OAAO,QAAQ;AAC7B,YAAI,OAAO,OAAO,QAAQ,YAAY,YAAY;AAChD,oBAAU,OAAO,QAAQ,QAAQ,KAAK,SAAS;AAAA,QACjD;AACA,YAAI,MAAM,OAAO,SAAS,aAAa,YAAY,SAAS;AAAA,UAC1D;AAAA,UACA,IAAI;AAAA,QACN,CAAC;AACD,eAAO,QAAQ,QAAQ,OAAO,SAAS,KAAK,SAAS,SAAU,MAAM,KAAK;AACxE,sBAAY;AACZ,oBAAU,KAAK,IAAI;AACnB,mBAAS,KAAK,GAAG;AACjB,cAAI,aAAa,UAAU,QAAQ;AACjC,gBAAI,OAAO,aAAa,WAAY,UAAS,WAAW,QAAQ;AAAA,UAClE;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AACvB,UAAI,SAAS;AACb,UAAI,iBAAiB,KAAK,UACxB,mBAAmB,eAAe,kBAClC,gBAAgB,eAAe,eAC/B,SAAS,eAAe;AAC1B,UAAI,kBAAkB,iBAAiB;AACvC,UAAI,mBAAmB,gBAAgB,YAAY,MAAM,SAAU;AACnE,UAAI,SAAS,CAAC;AACd,UAAI,SAAS,SAASU,QAAO,KAAK;AAChC,YAAI,OAAO,cAAc,mBAAmB,GAAG;AAC/C,aAAK,QAAQ,SAAU,GAAG;AACxB,cAAI,OAAO,QAAQ,CAAC,IAAI,EAAG,QAAO,KAAK,CAAC;AAAA,QAC1C,CAAC;AAAA,MACH;AACA,aAAO,eAAe;AACtB,UAAI,KAAK,WAAW,QAAS,MAAK,WAAW,QAAQ,QAAQ,SAAU,GAAG;AACxE,eAAO,OAAO,CAAC;AAAA,MACjB,CAAC;AACD,aAAO,QAAQ,SAAU,KAAK;AAC5B,eAAO,WAAW,GAAG,QAAQ,SAAU,IAAI;AACzC,2BAAiB,KAAK,KAAK,IAAI,QAAQ,MAAM,MAAM,SAAU,KAAK,MAAM;AACtE,gBAAI,IAAK,QAAO,KAAK,qBAAqB,OAAO,IAAI,gBAAgB,EAAE,OAAO,KAAK,SAAS,GAAG,GAAG;AAClG,gBAAI,CAAC,OAAO,KAAM,QAAO,IAAI,oBAAoB,OAAO,IAAI,gBAAgB,EAAE,OAAO,GAAG,GAAG,IAAI;AAC/F,6BAAiB,OAAO,GAAG,OAAO,KAAK,GAAG,EAAE,OAAO,EAAE,GAAG,KAAK,IAAI;AAAA,UACnE,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF,CAAC,CAAC;AACJ,EAAE;AACF,QAAQ,OAAO;AACf,IAAO,cAAQ;", "names": ["globalThis", "exports", "global", "fetch", "request", "fetchApi", "o", "r", "_typeof", "o", "addQueryString", "fetchIt", "resolver", "requestWithFetch", "requestWithXmlHttpRequest", "request", "_typeof", "o", "ownKeys", "r", "_objectSpread", "_defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "getDefaults", "Backend", "append"]}
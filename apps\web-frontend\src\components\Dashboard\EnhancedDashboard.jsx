import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  IconButton,
  Button,
  LinearProgress,
  Fade,
  Skeleton,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  People as PeopleIcon,
  School as SchoolIcon,
  Assessment as AssessmentIcon,
  Notifications as NotificationsIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
  MoreVert as MoreVertIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

// Enhanced metric card with animations and better visual hierarchy
const MetricCard = ({ 
  title, 
  value, 
  change, 
  trend, 
  icon: Icon, 
  color = 'primary',
  loading = false,
  onClick 
}) => {
  const theme = useTheme();
  const [isHovered, setIsHovered] = useState(false);

  if (loading) {
    return (
      <Card sx={{ height: '100%', minHeight: 140 }}>
        <CardContent>
          <Skeleton variant="circular" width={48} height={48} />
          <Skeleton variant="text" width="60%" sx={{ mt: 2 }} />
          <Skeleton variant="text" width="40%" />
          <Skeleton variant="text" width="80%" />
        </CardContent>
      </Card>
    );
  }

  const trendColor = trend === 'up' ? theme.palette.success.main : 
                    trend === 'down' ? theme.palette.error.main : 
                    theme.palette.text.secondary;

  const TrendIcon = trend === 'up' ? TrendingUpIcon : 
                   trend === 'down' ? TrendingDownIcon : null;

  return (
    <Fade in timeout={300}>
      <Card
        sx={{
          height: '100%',
          minHeight: 140,
          cursor: onClick ? 'pointer' : 'default',
          transition: 'all 0.3s ease-in-out',
          transform: isHovered ? 'translateY(-4px)' : 'translateY(0)',
          boxShadow: isHovered ? theme.shadows[8] : theme.shadows[1],
          '&:hover': {
            '& .metric-icon': {
              transform: 'scale(1.1)',
            },
          },
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={onClick}
      >
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>
            <Box sx={{ flex: 1 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {title}
              </Typography>
              <Typography variant="h4" component="div" sx={{ fontWeight: 700, mb: 1 }}>
                {value}
              </Typography>
              {change && (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  {TrendIcon && (
                    <TrendIcon sx={{ fontSize: 16, color: trendColor }} />
                  )}
                  <Typography variant="body2" sx={{ color: trendColor, fontWeight: 500 }}>
                    {change}
                  </Typography>
                </Box>
              )}
            </Box>
            <Avatar
              className="metric-icon"
              sx={{
                bgcolor: `${color}.main`,
                width: 48,
                height: 48,
                transition: 'transform 0.2s ease-in-out',
              }}
            >
              <Icon />
            </Avatar>
          </Box>
        </CardContent>
      </Card>
    </Fade>
  );
};

// Quick action button with enhanced styling
const QuickActionButton = ({ icon: Icon, label, onClick, color = 'primary' }) => {
  const theme = useTheme();
  
  return (
    <Button
      variant="contained"
      startIcon={<Icon />}
      onClick={onClick}
      sx={{
        borderRadius: 2,
        py: 1.5,
        px: 3,
        textTransform: 'none',
        fontWeight: 600,
        boxShadow: theme.shadows[2],
        '&:hover': {
          boxShadow: theme.shadows[4],
          transform: 'translateY(-1px)',
        },
        transition: 'all 0.2s ease-in-out',
      }}
      color={color}
    >
      {label}
    </Button>
  );
};

// Recent activity item with better visual design
const ActivityItem = ({ activity, index }) => {
  const theme = useTheme();
  
  const getActivityColor = (type) => {
    switch (type) {
      case 'success': return theme.palette.success.main;
      case 'warning': return theme.palette.warning.main;
      case 'error': return theme.palette.error.main;
      default: return theme.palette.info.main;
    }
  };

  return (
    <Fade in timeout={300 + index * 100}>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          p: 2,
          borderRadius: 2,
          transition: 'background-color 0.2s ease-in-out',
          '&:hover': {
            bgcolor: 'action.hover',
          },
        }}
      >
        <Box
          sx={{
            width: 8,
            height: 8,
            borderRadius: '50%',
            bgcolor: getActivityColor(activity.type),
            flexShrink: 0,
          }}
        />
        <Box sx={{ flex: 1, minWidth: 0 }}>
          <Typography variant="body2" sx={{ fontWeight: 500 }}>
            {activity.title}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {activity.time}
          </Typography>
        </Box>
        <Chip
          label={activity.category}
          size="small"
          variant="outlined"
          sx={{ fontSize: '0.75rem' }}
        />
      </Box>
    </Fade>
  );
};

// Main Enhanced Dashboard Component
const EnhancedDashboard = () => {
  const { t } = useTranslation(['dashboard', 'common']);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const [loading, setLoading] = useState(true);
  const [metrics, setMetrics] = useState({});
  const [recentActivities, setRecentActivities] = useState([]);

  // Simulate data loading
  useEffect(() => {
    const loadDashboardData = async () => {
      setLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setMetrics({
        totalStudents: { value: '1,247', change: '+12 this month', trend: 'up' },
        activeClasses: { value: '24', change: '+2 new classes', trend: 'up' },
        pendingReports: { value: '8', change: '-3 from last week', trend: 'down' },
        avgPerformance: { value: '78.5%', change: '****% improvement', trend: 'up' },
      });

      setRecentActivities([
        {
          id: 1,
          title: 'New SWOT analysis completed for Class 10-A',
          time: '2 minutes ago',
          type: 'success',
          category: 'Analysis'
        },
        {
          id: 2,
          title: 'Parent meeting scheduled for tomorrow',
          time: '15 minutes ago',
          type: 'info',
          category: 'Meeting'
        },
        {
          id: 3,
          title: 'Low attendance alert for 5 students',
          time: '1 hour ago',
          type: 'warning',
          category: 'Attendance'
        },
        {
          id: 4,
          title: 'Monthly report generation completed',
          time: '2 hours ago',
          type: 'success',
          category: 'Report'
        },
      ]);
      
      setLoading(false);
    };

    loadDashboardData();
  }, []);

  const handleRefresh = () => {
    setLoading(true);
    // Simulate refresh
    setTimeout(() => setLoading(false), 1000);
  };

  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      {/* Header Section */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box>
            <Typography variant="h4" component="h1" sx={{ fontWeight: 700, mb: 1 }}>
              {t('dashboard:welcomeMessage')}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {t('dashboard:dashboardSubtitle', 'Monitor student progress and manage your classes effectively')}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <IconButton onClick={handleRefresh} disabled={loading}>
              <RefreshIcon />
            </IconButton>
            <IconButton>
              <NotificationsIcon />
            </IconButton>
            <IconButton>
              <MoreVertIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Quick Actions */}
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mb: 3 }}>
          <QuickActionButton
            icon={AddIcon}
            label={t('dashboard:addStudent')}
            onClick={() => console.log('Add student')}
          />
          <QuickActionButton
            icon={AssessmentIcon}
            label={t('dashboard:generateReport')}
            onClick={() => console.log('Generate report')}
            color="secondary"
          />
          <QuickActionButton
            icon={SchoolIcon}
            label={t('dashboard:manageClasses')}
            onClick={() => console.log('Manage classes')}
            color="success"
          />
        </Box>
      </Box>

      {/* Metrics Grid */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} lg={3}>
          <MetricCard
            title={t('dashboard:totalStudents')}
            value={metrics.totalStudents?.value}
            change={metrics.totalStudents?.change}
            trend={metrics.totalStudents?.trend}
            icon={PeopleIcon}
            color="primary"
            loading={loading}
            onClick={() => console.log('View students')}
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <MetricCard
            title={t('dashboard:activeClasses')}
            value={metrics.activeClasses?.value}
            change={metrics.activeClasses?.change}
            trend={metrics.activeClasses?.trend}
            icon={SchoolIcon}
            color="secondary"
            loading={loading}
            onClick={() => console.log('View classes')}
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <MetricCard
            title={t('dashboard:pendingReports')}
            value={metrics.pendingReports?.value}
            change={metrics.pendingReports?.change}
            trend={metrics.pendingReports?.trend}
            icon={AssessmentIcon}
            color="warning"
            loading={loading}
            onClick={() => console.log('View reports')}
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <MetricCard
            title={t('dashboard:avgPerformance')}
            value={metrics.avgPerformance?.value}
            change={metrics.avgPerformance?.change}
            trend={metrics.avgPerformance?.trend}
            icon={TrendingUpIcon}
            color="success"
            loading={loading}
            onClick={() => console.log('View performance')}
          />
        </Grid>
      </Grid>

      {/* Content Grid */}
      <Grid container spacing={3}>
        {/* Recent Activity */}
        <Grid item xs={12} lg={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                {t('dashboard:recentActivity')}
              </Typography>
              {loading ? (
                <Box>
                  {[...Array(4)].map((_, index) => (
                    <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Skeleton variant="circular" width={8} height={8} />
                      <Box sx={{ flex: 1 }}>
                        <Skeleton variant="text" width="80%" />
                        <Skeleton variant="text" width="40%" />
                      </Box>
                      <Skeleton variant="rectangular" width={60} height={24} />
                    </Box>
                  ))}
                </Box>
              ) : (
                <Box>
                  {recentActivities.map((activity, index) => (
                    <ActivityItem key={activity.id} activity={activity} index={index} />
                  ))}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Performance Overview */}
        <Grid item xs={12} lg={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                {t('dashboard:performanceOverview')}
              </Typography>
              {loading ? (
                <Box>
                  {[...Array(3)].map((_, index) => (
                    <Box key={index} sx={{ mb: 3 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Skeleton variant="text" width="40%" />
                        <Skeleton variant="text" width="20%" />
                      </Box>
                      <Skeleton variant="rectangular" height={8} />
                    </Box>
                  ))}
                </Box>
              ) : (
                <Box>
                  {[
                    { subject: 'Mathematics', score: 85, color: 'primary' },
                    { subject: 'Science', score: 78, color: 'secondary' },
                    { subject: 'English', score: 92, color: 'success' },
                  ].map((item, index) => (
                    <Fade in timeout={300 + index * 100} key={item.subject}>
                      <Box sx={{ mb: 3 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {item.subject}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {item.score}%
                          </Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={item.score}
                          color={item.color}
                          sx={{
                            height: 8,
                            borderRadius: 4,
                            bgcolor: 'action.hover',
                          }}
                        />
                      </Box>
                    </Fade>
                  ))}
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default EnhancedDashboard;

import React, { Suspense, lazy } from 'react';
import { Box, CircularProgress, Skeleton, Fade } from '@mui/material';
import { useIntersectionObserver } from '../../hooks/useIntersectionObserver';

// Enhanced loading component with skeleton screens
const LoadingFallback = ({ 
  type = 'default', 
  height = 200, 
  showProgress = true,
  message = 'Loading...' 
}) => {
  const renderSkeleton = () => {
    switch (type) {
      case 'dashboard':
        return (
          <Box sx={{ p: 3 }}>
            {/* Header skeleton */}
            <Box sx={{ mb: 4 }}>
              <Skeleton variant="text" width="40%" height={40} sx={{ mb: 1 }} />
              <Skeleton variant="text" width="60%" height={24} />
            </Box>
            
            {/* Metrics cards skeleton */}
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: 3, mb: 4 }}>
              {[...Array(4)].map((_, index) => (
                <Box key={index} sx={{ p: 3, border: '1px solid #e0e0e0', borderRadius: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Box sx={{ flex: 1 }}>
                      <Skeleton variant="text" width="60%" height={20} />
                      <Skeleton variant="text" width="40%" height={32} sx={{ my: 1 }} />
                      <Skeleton variant="text" width="50%" height={16} />
                    </Box>
                    <Skeleton variant="circular" width={48} height={48} />
                  </Box>
                </Box>
              ))}
            </Box>
            
            {/* Content area skeleton */}
            <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', lg: '1fr 1fr' }, gap: 3 }}>
              <Box sx={{ p: 3, border: '1px solid #e0e0e0', borderRadius: 2 }}>
                <Skeleton variant="text" width="40%" height={24} sx={{ mb: 2 }} />
                {[...Array(4)].map((_, index) => (
                  <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    <Skeleton variant="circular" width={8} height={8} />
                    <Box sx={{ flex: 1 }}>
                      <Skeleton variant="text" width="80%" />
                      <Skeleton variant="text" width="40%" />
                    </Box>
                    <Skeleton variant="rectangular" width={60} height={24} />
                  </Box>
                ))}
              </Box>
              <Box sx={{ p: 3, border: '1px solid #e0e0e0', borderRadius: 2 }}>
                <Skeleton variant="text" width="40%" height={24} sx={{ mb: 2 }} />
                {[...Array(3)].map((_, index) => (
                  <Box key={index} sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Skeleton variant="text" width="40%" />
                      <Skeleton variant="text" width="20%" />
                    </Box>
                    <Skeleton variant="rectangular" height={8} />
                  </Box>
                ))}
              </Box>
            </Box>
          </Box>
        );
        
      case 'studentList':
        return (
          <Box sx={{ p: 3 }}>
            {/* Header and search skeleton */}
            <Box sx={{ mb: 3 }}>
              <Skeleton variant="text" width="40%" height={40} sx={{ mb: 2 }} />
              <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
                <Skeleton variant="rectangular" height={56} sx={{ flex: 1 }} />
                <Skeleton variant="rectangular" width={120} height={56} />
                <Skeleton variant="rectangular" width={140} height={56} />
              </Box>
            </Box>
            
            {/* Student cards skeleton */}
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))', gap: 3 }}>
              {[...Array(6)].map((_, index) => (
                <Box key={index} sx={{ p: 3, border: '1px solid #e0e0e0', borderRadius: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2, mb: 2 }}>
                    <Skeleton variant="circular" width={56} height={56} />
                    <Box sx={{ flex: 1 }}>
                      <Skeleton variant="text" width="80%" height={24} />
                      <Skeleton variant="text" width="60%" height={20} />
                      <Skeleton variant="text" width="40%" height={16} />
                    </Box>
                    <Skeleton variant="circular" width={24} height={24} />
                  </Box>
                  <Box sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Skeleton variant="text" width="50%" />
                      <Skeleton variant="text" width="20%" />
                    </Box>
                    <Skeleton variant="rectangular" height={6} />
                  </Box>
                  <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                    <Skeleton variant="rectangular" width={80} height={24} />
                    <Skeleton variant="rectangular" width={60} height={24} />
                  </Box>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Skeleton variant="rectangular" height={36} sx={{ flex: 1 }} />
                    <Skeleton variant="rectangular" height={36} sx={{ flex: 1 }} />
                  </Box>
                </Box>
              ))}
            </Box>
          </Box>
        );
        
      case 'swotAnalysis':
        return (
          <Box sx={{ p: 3 }}>
            {/* Header skeleton */}
            <Box sx={{ mb: 4 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                <Box>
                  <Skeleton variant="text" width="40%" height={40} />
                  <Skeleton variant="text" width="60%" height={24} />
                </Box>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Skeleton variant="rectangular" width={100} height={36} />
                  <Skeleton variant="rectangular" width={80} height={36} />
                </Box>
              </Box>
            </Box>
            
            {/* SWOT quadrants skeleton */}
            <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 3, mb: 4 }}>
              {['Strengths', 'Weaknesses', 'Opportunities', 'Threats'].map((title, index) => (
                <Box key={index} sx={{ border: '2px solid #e0e0e0', borderRadius: 3, overflow: 'hidden' }}>
                  <Box sx={{ p: 2, bgcolor: '#f5f5f5' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Skeleton variant="circular" width={24} height={24} />
                        <Skeleton variant="text" width={100} />
                      </Box>
                      <Skeleton variant="rectangular" width={40} height={24} />
                    </Box>
                  </Box>
                  <Box sx={{ p: 3 }}>
                    {[...Array(3)].map((_, itemIndex) => (
                      <Box key={itemIndex} sx={{ display: 'flex', alignItems: 'flex-start', gap: 2, mb: 2 }}>
                        <Skeleton variant="circular" width={16} height={16} sx={{ mt: 0.5 }} />
                        <Box sx={{ flex: 1 }}>
                          <Skeleton variant="text" width="90%" />
                          <Skeleton variant="text" width="60%" />
                        </Box>
                      </Box>
                    ))}
                  </Box>
                </Box>
              ))}
            </Box>
            
            {/* Recommendations skeleton */}
            <Box sx={{ border: '1px solid #e0e0e0', borderRadius: 3, p: 3 }}>
              <Skeleton variant="text" width="40%" height={32} sx={{ mb: 3 }} />
              {[...Array(2)].map((_, index) => (
                <Box key={index} sx={{ p: 3, border: '1px solid #e0e0e0', borderRadius: 2, mb: 2 }}>
                  <Skeleton variant="text" width="80%" height={24} sx={{ mb: 2 }} />
                  <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                    <Skeleton variant="rectangular" width={60} height={24} />
                    <Skeleton variant="rectangular" width={80} height={24} />
                    <Skeleton variant="rectangular" width={100} height={24} />
                  </Box>
                </Box>
              ))}
            </Box>
          </Box>
        );
        
      default:
        return (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height,
              gap: 2,
            }}
          >
            {showProgress && (
              <CircularProgress 
                size={40} 
                thickness={4}
                sx={{ 
                  color: 'primary.main',
                  '& .MuiCircularProgress-circle': {
                    strokeLinecap: 'round',
                  },
                }}
              />
            )}
            <Skeleton variant="text" width="60%" height={24} />
          </Box>
        );
    }
  };

  return (
    <Fade in timeout={300}>
      <Box>
        {renderSkeleton()}
      </Box>
    </Fade>
  );
};

// Lazy load wrapper with intersection observer
const LazyLoadWrapper = ({ 
  children, 
  fallback, 
  threshold = 0.1, 
  rootMargin = '50px',
  once = true 
}) => {
  const [ref, isIntersecting] = useIntersectionObserver({
    threshold,
    rootMargin,
    once,
  });

  return (
    <div ref={ref}>
      {isIntersecting ? (
        <Suspense fallback={fallback}>
          {children}
        </Suspense>
      ) : (
        fallback
      )}
    </div>
  );
};

// Higher-order component for lazy loading
export const withLazyLoading = (Component, fallbackType = 'default') => {
  return React.forwardRef((props, ref) => (
    <LazyLoadWrapper
      fallback={<LoadingFallback type={fallbackType} />}
      {...props}
    >
      <Component {...props} ref={ref} />
    </LazyLoadWrapper>
  ));
};

// Preload function for critical components
export const preloadComponent = (componentImport) => {
  if (typeof componentImport === 'function') {
    componentImport();
  }
};

// Progressive image loading component
export const ProgressiveImage = ({ 
  src, 
  placeholder, 
  alt, 
  className,
  ...props 
}) => {
  const [imageLoaded, setImageLoaded] = React.useState(false);
  const [imageSrc, setImageSrc] = React.useState(placeholder);

  React.useEffect(() => {
    const img = new Image();
    img.onload = () => {
      setImageSrc(src);
      setImageLoaded(true);
    };
    img.src = src;
  }, [src]);

  return (
    <Box
      component="img"
      src={imageSrc}
      alt={alt}
      className={className}
      sx={{
        transition: 'opacity 0.3s ease-in-out',
        opacity: imageLoaded ? 1 : 0.7,
        filter: imageLoaded ? 'none' : 'blur(2px)',
        ...props.sx,
      }}
      {...props}
    />
  );
};

export { LoadingFallback, LazyLoadWrapper };
export default LazyLoadWrapper;

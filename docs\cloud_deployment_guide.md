# Cloud Deployment Guide
# SWOT Analysis Platform

This guide provides instructions for deploying the SWOT Analysis Platform to major cloud providers.

## Table of Contents

1. [AWS Deployment](#aws-deployment)
2. [Azure Deployment](#azure-deployment)
3. [Google Cloud Platform Deployment](#google-cloud-platform-deployment)
4. [General Cloud Considerations](#general-cloud-considerations)

## AWS Deployment

### Option 1: Amazon ECS with Fargate

This serverless approach uses AWS Elastic Container Service (ECS) with Fargate.

#### Prerequisites

- AWS Account with appropriate permissions
- AWS CLI installed and configured
- Docker installed locally
- Amazon ECR repository created for the application

#### Step 1: Build and Push Docker Image

```bash
# Login to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.us-east-1.amazonaws.com

# Build the image
docker build -t swot-platform -f Dockerfile.prod .

# Tag the image
docker tag swot-platform:latest ${AWS_ACCOUNT_ID}.dkr.ecr.us-east-1.amazonaws.com/swot-platform:latest

# Push the image
docker push ${AWS_ACCOUNT_ID}.dkr.ecr.us-east-1.amazonaws.com/swot-platform:latest
```

#### Step 2: Create RDS Database

1. Go to the Amazon RDS console
2. Click "Create database"
3. Select PostgreSQL
4. Choose "Production" template
5. Set up credentials (save them securely)
6. Configure advanced settings:
   - VPC: Select your VPC
   - Subnet group: Create or select existing
   - Security group: Create or select existing
   - Database name: `swot_platform`
   - Backup: Enable automatic backups
   - Encryption: Enable encryption at rest

#### Step 3: Create ElastiCache for Redis

1. Go to the Amazon ElastiCache console
2. Click "Create"
3. Select Redis
4. Configure settings:
   - Name: `swot-platform-cache`
   - Node type: Select appropriate size (e.g., cache.t3.small)
   - Number of replicas: At least 1 for production
   - Multi-AZ: Enable for production
   - VPC: Same as RDS

#### Step 4: Create ECS Cluster and Task Definition

1. Go to the Amazon ECS console
2. Create a new cluster (Fargate)
3. Create a Task Definition:
   - Fargate compatibility
   - Task memory: 2GB (adjust as needed)
   - Task CPU: 1vCPU (adjust as needed)
   - Container definitions:
     - Image: ECR image URL
     - Port mappings: 8000
     - Environment variables:
       - DB_HOST: RDS endpoint
       - DB_TYPE: postgresql
       - DB_NAME: swot_platform
       - DB_USER: database user
       - DB_PASSWORD: fetch from AWS Secrets Manager
       - CACHE_TYPE: redis
       - CACHE_REDIS_URL: ElastiCache endpoint
       - Other configuration variables

#### Step 5: Create ECS Service

1. In the ECS Cluster, create a new Service:
   - Launch type: Fargate
   - Task Definition: Select the one created above
   - Service name: `swot-platform-service`
   - Number of tasks: 2 (adjust as needed)
   - Deployment type: Rolling update
   - Load balancer:
     - Create or select Application Load Balancer
     - Configure listener on port 443
     - Configure target group
   - Auto Scaling (optional):
     - Minimum tasks: 2
     - Maximum tasks: 10
     - Scaling policies based on CPU/memory utilization

#### Step 6: Configure Load Balancer and SSL

1. Go to EC2 console > Load Balancers
2. Configure SSL certificate:
   - Create or import certificate in ACM
   - Add HTTPS listener using the certificate
3. Configure security group to allow ports 80 and 443
4. Configure DNS (Route 53) to point to the ALB endpoint

### Option 2: AWS Elastic Beanstalk

For a simpler deployment option using Elastic Beanstalk:

#### Step 1: Prepare Application

Create an Elastic Beanstalk configuration file `.ebextensions/01_environment.config`:

```yaml
option_settings:
  aws:elasticbeanstalk:application:environment:
    APP_NAME: SWOT Analysis Platform
    DEBUG: False
    DB_TYPE: postgresql
    
  aws:elasticbeanstalk:container:python:
    WSGIPath: wsgi:app
    
  aws:autoscaling:asg:
    MinSize: 2
    MaxSize: 10
    
  aws:autoscaling:launchconfiguration:
    InstanceType: t3.small
    
  aws:elasticbeanstalk:environment:
    LoadBalancerType: application
```

#### Step 2: Deploy with Elastic Beanstalk CLI

```bash
# Initialize EB CLI
eb init -p python-3.9 swot-platform --region us-east-1

# Create environment
eb create swot-platform-prod --database --database.engine postgres --database.instance db.t3.small --elb-type application

# Deploy application
eb deploy

# Open application
eb open
```

## Azure Deployment

### Azure Container Apps

#### Step 1: Create Azure Database for PostgreSQL

```bash
# Create resource group
az group create --name swot-platform-rg --location eastus

# Create PostgreSQL server
az postgres server create \
  --resource-group swot-platform-rg \
  --name swot-platform-db \
  --location eastus \
  --admin-user swotapp \
  --admin-password "SecurePassword123!" \
  --sku-name GP_Gen5_2 \
  --version 12

# Create database
az postgres db create \
  --resource-group swot-platform-rg \
  --server-name swot-platform-db \
  --name swot_platform
```

#### Step 2: Create Azure Cache for Redis

```bash
az redis create \
  --resource-group swot-platform-rg \
  --name swot-platform-cache \
  --location eastus \
  --sku Basic \
  --vm-size c0
```

#### Step 3: Create Azure Container Registry

```bash
# Create registry
az acr create \
  --resource-group swot-platform-rg \
  --name swotplatformregistry \
  --sku Basic

# Login to registry
az acr login --name swotplatformregistry

# Build and push image
docker build -t swot-platform -f Dockerfile.prod .
docker tag swot-platform swotplatformregistry.azurecr.io/swot-platform:latest
docker push swotplatformregistry.azurecr.io/swot-platform:latest
```

#### Step 4: Deploy Azure Container Apps

```bash
# Create Container Apps environment
az containerapp env create \
  --resource-group swot-platform-rg \
  --name swot-platform-env \
  --location eastus

# Create Container App
az containerapp create \
  --resource-group swot-platform-rg \
  --name swot-platform \
  --environment swot-platform-env \
  --image swotplatformregistry.azurecr.io/swot-platform:latest \
  --target-port 8000 \
  --ingress external \
  --registry-server swotplatformregistry.azurecr.io \
  --min-replicas 2 \
  --max-replicas 10 \
  --env-vars \
    APP_NAME="SWOT Analysis Platform" \
    DEBUG=False \
    DB_TYPE=postgresql \
    DB_NAME=swot_platform \
    DB_USER=swotapp \
    DB_PASSWORD="SecurePassword123!" \
    DB_HOST=swot-platform-db.postgres.database.azure.com \
    CACHE_TYPE=redis \
    CACHE_REDIS_URL=redis://swot-platform-cache.redis.cache.windows.net:6380
```

#### Step 5: Configure TLS and Custom Domain

```bash
# Add custom domain
az containerapp hostname add \
  --resource-group swot-platform-rg \
  --name swot-platform \
  --hostname swot-platform.example.com

# Add certificate
az containerapp certificate add \
  --resource-group swot-platform-rg \
  --name swot-platform \
  --hostname swot-platform.example.com \
  --certificate-file ./certificate.pfx \
  --password "CertificatePassword"
```

## Google Cloud Platform Deployment

### Google Kubernetes Engine (GKE)

#### Step 1: Create Cloud SQL Instance

```bash
# Create PostgreSQL instance
gcloud sql instances create swot-platform-db \
  --database-version=POSTGRES_13 \
  --tier=db-g1-small \
  --region=us-central1 \
  --storage-type=SSD \
  --storage-size=10GB \
  --availability-type=REGIONAL \
  --backup-start-time=04:00 \
  --enable-point-in-time-recovery

# Create database
gcloud sql databases create swot_platform --instance=swot-platform-db

# Create user
gcloud sql users create swotapp \
  --instance=swot-platform-db \
  --password="SecurePassword123!"
```

#### Step 2: Create Redis Instance

```bash
# Create Redis instance
gcloud redis instances create swot-platform-cache \
  --size=1 \
  --region=us-central1 \
  --tier=basic
```

#### Step 3: Create GKE Cluster

```bash
# Create cluster
gcloud container clusters create swot-platform-cluster \
  --num-nodes=2 \
  --machine-type=e2-standard-2 \
  --region=us-central1

# Get credentials
gcloud container clusters get-credentials swot-platform-cluster --region=us-central1
```

#### Step 4: Build and Push Docker Image

```bash
# Configure Docker for Artifact Registry
gcloud auth configure-docker us-central1-docker.pkg.dev

# Create Artifact Registry repository
gcloud artifacts repositories create swot-platform \
  --repository-format=docker \
  --location=us-central1 \
  --description="SWOT Analysis Platform Repository"

# Build and push image
docker build -t us-central1-docker.pkg.dev/[PROJECT-ID]/swot-platform/swot-platform:latest -f Dockerfile.prod .
docker push us-central1-docker.pkg.dev/[PROJECT-ID]/swot-platform/swot-platform:latest
```

#### Step 5: Create Kubernetes Deployment and Service

Create deployment file `k8s-deployment.yaml`:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: swot-platform
spec:
  replicas: 3
  selector:
    matchLabels:
      app: swot-platform
  template:
    metadata:
      labels:
        app: swot-platform
    spec:
      containers:
      - name: swot-platform
        image: us-central1-docker.pkg.dev/[PROJECT-ID]/swot-platform/swot-platform:latest
        ports:
        - containerPort: 8000
        env:
        - name: APP_NAME
          value: "SWOT Analysis Platform"
        - name: DEBUG
          value: "False"
        - name: DB_TYPE
          value: "postgresql"
        - name: DB_NAME
          value: "swot_platform"
        - name: DB_USER
          value: "swotapp"
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: password
        - name: DB_HOST
          value: "127.0.0.1"
        - name: DB_PORT
          value: "5432"
        - name: CACHE_TYPE
          value: "redis"
        - name: CACHE_REDIS_URL
          value: "redis://[REDIS-IP]:6379/0"
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 512Mi
      - name: cloud-sql-proxy
        image: gcr.io/cloudsql-docker/gce-proxy:latest
        command:
          - "/cloud_sql_proxy"
          - "-instances=[PROJECT-ID]:us-central1:swot-platform-db=tcp:5432"
        securityContext:
          runAsNonRoot: true
        resources:
          requests:
            cpu: 50m
            memory: 128Mi
          limits:
            cpu: 200m
            memory: 256Mi
---
apiVersion: v1
kind: Service
metadata:
  name: swot-platform-service
spec:
  selector:
    app: swot-platform
  ports:
  - port: 80
    targetPort: 8000
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: swot-platform-ingress
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "swot-platform-ip"
    networking.gke.io/managed-certificates: "swot-platform-cert"
spec:
  rules:
  - host: swot-platform.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: swot-platform-service
            port:
              number: 80
```

Deploy to GKE:

```bash
# Create secret for database password
kubectl create secret generic db-credentials --from-literal=password="SecurePassword123!"

# Apply deployment
kubectl apply -f k8s-deployment.yaml

# Create managed certificate
kubectl apply -f - <<EOF
apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: swot-platform-cert
spec:
  domains:
    - swot-platform.example.com
EOF

# Create static IP
gcloud compute addresses create swot-platform-ip --global
```

## General Cloud Considerations

### Cost Optimization

1. **Right-sizing Resources**:
   - Start with smaller instances and scale up as needed
   - Use auto-scaling to handle varying loads
   - Monitor resource utilization and adjust accordingly

2. **Reserved Instances/Committed Use**:
   - AWS: Reserved Instances
   - Azure: Reserved VM Instances
   - GCP: Committed Use Discounts

3. **Managed Services vs. Self-Managed**:
   - Consider managed database services over self-hosted
   - Use serverless options for variable workloads

### Security Best Practices

1. **Identity and Access Management**:
   - Use principle of least privilege
   - Implement Multi-Factor Authentication
   - Regularly audit access

2. **Network Security**:
   - Implement private networks (VPC/VNET)
   - Use security groups/firewall rules
   - Restrict database access to application only

3. **Data Protection**:
   - Enable encryption at rest and in transit
   - Implement regular backups
   - Consider geo-redundant storage

### Monitoring and Observability

1. **Logging Services**:
   - AWS: CloudWatch Logs
   - Azure: Azure Monitor
   - GCP: Cloud Logging

2. **Monitoring**:
   - Application metrics
   - Infrastructure metrics
   - Custom dashboards

3. **Alerting**:
   - Set up alerts for critical metrics
   - Configure notification channels
   - Implement runbooks for common issues

### Disaster Recovery

1. **Backup Strategy**:
   - Regular database backups
   - Configuration backups
   - Cross-region replication for critical data

2. **Recovery Plans**:
   - Document recovery procedures
   - Test recovery regularly
   - Define Recovery Time Objectives (RTO) and Recovery Point Objectives (RPO)

### Compliance Considerations

1. **Data Residency**:
   - Ensure data storage complies with local regulations
   - Consider multi-region deployment for global users

2. **Auditing**:
   - Enable audit logging
   - Regular compliance reviews
   - Document compliance measures

3. **Access Controls**:
   - Implement appropriate controls for student data
   - Ensure GDPR/FERPA compliance where applicable
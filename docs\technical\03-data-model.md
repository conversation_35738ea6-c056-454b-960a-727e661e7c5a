# Data Model Documentation

This document describes the data model used by the SWOT Analysis Platform, including entity relationships, database schema, and data formats.

## Entity-Relationship Diagram

The following diagram illustrates the relationships between the main entities in the system:

![Entity-Relationship Diagram](./entity-relationship-diagram.png)

## Database Schema

The platform uses a relational database with the following tables:

### Students

```sql
CREATE TABLE Students (
    student_id VARCHAR(10) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    grade_level INT NOT NULL,
    homeroom VARCHAR(10) NOT NULL,
    academic_year VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

Stores basic student information including:
- Unique identifier
- Name
- Grade level (e.g., 9, 10, 11, 12)
- Homeroom assignment
- Current academic year

### Guardians

```sql
CREATE TABLE Guardians (
    guardian_id VARCHAR(10) PRIMARY KEY,
    student_id VARCHAR(10) NOT NULL,
    name VARCHAR(100) NOT NULL,
    relationship VARCHAR(50) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES Students(student_id) ON DELETE CASCADE
);
```

Stores information about student guardians/parents including:
- Unique identifier
- Association with a student
- Guardian name
- Relationship type (e.g., Mother, Father, Guardian)
- Contact information

### AcademicPerformance

```sql
CREATE TABLE AcademicPerformance (
    performance_id VARCHAR(20) PRIMARY KEY,
    student_id VARCHAR(10) NOT NULL,
    quarter INT NOT NULL,
    academic_year VARCHAR(10) NOT NULL,
    overall_gpa DECIMAL(3,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES Students(student_id) ON DELETE CASCADE,
    UNIQUE KEY (student_id, quarter, academic_year)
);
```

Stores quarterly academic performance records including:
- Unique identifier
- Student association
- Quarter (1, 2, 3, or 4)
- Academic year
- Overall GPA for the period

### Subjects

```sql
CREATE TABLE Subjects (
    subject_id VARCHAR(20) PRIMARY KEY,
    performance_id VARCHAR(20) NOT NULL,
    subject_name VARCHAR(50) NOT NULL,
    score DECIMAL(5,2) NOT NULL,
    grade VARCHAR(5) NOT NULL,
    teacher_comments TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (performance_id) REFERENCES AcademicPerformance(performance_id) ON DELETE CASCADE
);
```

Stores subject-specific performance information including:
- Unique identifier
- Association with an academic performance record
- Subject name
- Numerical score
- Letter grade
- Optional teacher comments

### Attendance

```sql
CREATE TABLE Attendance (
    attendance_id VARCHAR(20) PRIMARY KEY,
    student_id VARCHAR(10) NOT NULL,
    date DATE NOT NULL,
    status ENUM('present', 'absent', 'excused') NOT NULL,
    tardiness INT DEFAULT 0,
    excused BOOLEAN DEFAULT FALSE,
    reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES Students(student_id) ON DELETE CASCADE,
    UNIQUE KEY (student_id, date)
);
```

Stores daily attendance records including:
- Unique identifier
- Student association
- Date
- Attendance status
- Minutes late (if tardy)
- Excused status and reason (if absent)

### QuarterlyAttendance

```sql
CREATE TABLE QuarterlyAttendance (
    summary_id VARCHAR(20) PRIMARY KEY,
    student_id VARCHAR(10) NOT NULL,
    quarter INT NOT NULL,
    academic_year VARCHAR(10) NOT NULL,
    present_days INT NOT NULL,
    absent_days INT NOT NULL,
    tardy_days INT NOT NULL,
    attendance_rate DECIMAL(5,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES Students(student_id) ON DELETE CASCADE,
    UNIQUE KEY (student_id, quarter, academic_year)
);
```

Stores quarterly attendance summaries including:
- Unique identifier
- Student association
- Quarter and academic year
- Counts of present, absent, and tardy days
- Overall attendance rate

### BehavioralIncidents

```sql
CREATE TABLE BehavioralIncidents (
    incident_id VARCHAR(20) PRIMARY KEY,
    student_id VARCHAR(10) NOT NULL,
    date DATE NOT NULL,
    type ENUM('positive', 'negative') NOT NULL,
    category VARCHAR(50),
    description TEXT NOT NULL,
    action_taken VARCHAR(255),
    reported_by VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES Students(student_id) ON DELETE CASCADE
);
```

Stores behavioral incident records including:
- Unique identifier
- Student association
- Date of incident
- Type (positive or negative)
- Category (e.g., Classroom Behavior, Academic Integrity)
- Description
- Actions taken (if any)
- Reporting staff member

### BehavioralSummary

```sql
CREATE TABLE BehavioralSummary (
    summary_id VARCHAR(20) PRIMARY KEY,
    student_id VARCHAR(10) NOT NULL,
    quarter INT NOT NULL,
    academic_year VARCHAR(10) NOT NULL,
    positive_incidents INT NOT NULL DEFAULT 0,
    negative_incidents INT NOT NULL DEFAULT 0,
    behavior_trend ENUM('improving', 'stable', 'declining') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES Students(student_id) ON DELETE CASCADE,
    UNIQUE KEY (student_id, quarter, academic_year)
);
```

Stores quarterly behavioral summaries including:
- Unique identifier
- Student association
- Quarter and academic year
- Counts of positive and negative incidents
- Overall behavioral trend

### ExtracurricularActivities

```sql
CREATE TABLE ExtracurricularActivities (
    activity_id VARCHAR(20) PRIMARY KEY,
    student_id VARCHAR(10) NOT NULL,
    academic_year VARCHAR(10) NOT NULL,
    activity_name VARCHAR(100) NOT NULL,
    role VARCHAR(50) NOT NULL,
    hours_per_week DECIMAL(4,2) NOT NULL,
    advisor VARCHAR(100) NOT NULL,
    attendance_rate DECIMAL(5,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES Students(student_id) ON DELETE CASCADE
);
```

Stores extracurricular activity participation including:
- Unique identifier
- Student association
- Academic year
- Activity name
- Student's role
- Time commitment
- Advisor/coach
- Attendance/participation rate

### SWOTAnalysis

```sql
CREATE TABLE SWOTAnalysis (
    analysis_id VARCHAR(20) PRIMARY KEY,
    student_id VARCHAR(10) NOT NULL,
    quarter INT NOT NULL,
    academic_year VARCHAR(10) NOT NULL,
    analysis_date DATE NOT NULL,
    analysis_data JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES Students(student_id) ON DELETE CASCADE,
    UNIQUE KEY (student_id, quarter, academic_year)
);
```

Stores SWOT analysis results including:
- Unique identifier
- Student association
- Quarter and academic year
- Analysis date
- Complete analysis data in JSON format

### Users

```sql
CREATE TABLE Users (
    user_id VARCHAR(20) PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('admin', 'teacher', 'parent') NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

Stores user account information including:
- Unique identifier
- Username and secure password hash
- Role (admin, teacher, or parent)
- Contact information
- Name

### UserStudentAccess

```sql
CREATE TABLE UserStudentAccess (
    access_id VARCHAR(20) PRIMARY KEY,
    user_id VARCHAR(20) NOT NULL,
    student_id VARCHAR(10) NOT NULL,
    access_level ENUM('read', 'write', 'admin') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES Users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES Students(student_id) ON DELETE CASCADE,
    UNIQUE KEY (user_id, student_id)
);
```

Controls which users can access which student records including:
- Unique identifier
- User association
- Student association
- Access level (read, write, or admin)

## JSON Data Formats

### Student Profile

```json
{
  "student_id": "STU12345",
  "name": "Jane Doe",
  "grade_level": 9,
  "homeroom": "9A",
  "academic_year": "2024-2025"
}
```

### Academic Performance

```json
{
  "student_id": "STU12345",
  "academic_year": "2024-2025",
  "quarter": 1,
  "subjects": [
    {
      "subject_name": "Mathematics",
      "score": 87,
      "grade": "B+"
    },
    {
      "subject_name": "English",
      "score": 92,
      "grade": "A-"
    },
    /* Additional subjects */
  ],
  "overall_gpa": 3.7
}
```

### Attendance Data

```json
{
  "student_id": "STU12345",
  "academic_year": "2024-2025",
  "quarter": 1,
  "daily_records": [
    {
      "date": "2024-09-01",
      "status": "present",
      "tardiness": 0
    },
    {
      "date": "2024-09-02",
      "status": "absent",
      "excused": true,
      "reason": "Illness"
    },
    /* Additional daily records */
  ],
  "quarterly_summary": {
    "present_days": 43,
    "absent_days": 2,
    "tardy_days": 1,
    "attendance_rate": 95.6
  }
}
```

### Behavioral Data

```json
{
  "student_id": "STU12345",
  "academic_year": "2024-2025",
  "quarter": 1,
  "incidents": [
    {
      "date": "2024-09-15",
      "type": "positive",
      "description": "Helped a new student"
    },
    {
      "date": "2024-10-02",
      "type": "negative",
      "category": "Disruption",
      "description": "Talking during quiet work time"
    },
    /* Additional incidents */
  ],
  "quarterly_summary": {
    "positive_incidents": 3,
    "negative_incidents": 1,
    "behavior_trend": "improving"
  }
}
```

### Extracurricular Data

```json
{
  "student_id": "STU12345",
  "academic_year": "2024-2025",
  "activities": [
    {
      "activity_name": "Chess Club",
      "role": "Member",
      "hours_per_week": 2,
      "attendance_rate": 100,
      "advisor": "Mr. Johnson"
    },
    {
      "activity_name": "School Newspaper",
      "role": "Writer",
      "hours_per_week": 3,
      "attendance_rate": 92,
      "advisor": "Ms. Williams"
    },
    /* Additional activities */
  ],
  "total_extracurricular_hours": 5
}
```

### SWOT Analysis Result

```json
{
  "student_id": "STU12345",
  "name": "Jane Doe",
  "grade_level": 9,
  "academic_year": "2024-2025",
  "quarter": 1,
  "analysis_date": "2024-10-15",
  "strengths": [
    {
      "category": "Academic",
      "type": "Subject Strength",
      "description": "Excellence in Science (A, 95%)",
      "score": 95
    },
    /* Additional strengths */
  ],
  "weaknesses": [
    {
      "category": "Academic",
      "type": "Subject Weakness",
      "description": "Needs improvement in Physical Education (C-, 72%)",
      "score": 72
    },
    /* Additional weaknesses */
  ],
  "opportunities": [
    {
      "category": "Academic",
      "type": "Enrichment",
      "description": "Consider Science Club or science fair participation to develop strength in Science"
    },
    /* Additional opportunities */
  ],
  "threats": [
    {
      "category": "Attendance",
      "type": "Inconsistency",
      "description": "Occasional attendance issues may impact academic performance if they continue"
    },
    /* Additional threats */
  ]
}
```

## Data Flow Diagrams

### 1. Data Ingestion Flow

```
External Data Sources → Data Connectors → Data Validation → Data Transformers → Database Storage
```

### 2. SWOT Analysis Flow

```
Database → Data Loaders → SWOT Analyzer → Trend Analyzer → Comparison Engine → Analysis Storage
```

### 3. Visualization Flow

```
Analysis Results → Data Transformers → Visualization Module → Chart Generation → Interactive Dashboard
```

### 4. User Access Flow

```
User Request → Authentication → Authorization → Data Access Control → Data Retrieval → Response
```

## Data Access Patterns

### Common Queries

1. **Student Profile Retrieval**
   - Get complete student information including academic, attendance, behavioral, and extracurricular data
   - Filtered by student ID, time period, or data type

2. **Class-level Analysis**
   - Aggregate data for all students in a class
   - Calculate averages, distributions, and outliers

3. **Trend Analysis**
   - Compare performance across multiple time periods
   - Identify patterns and trajectory

4. **Comparative Analysis**
   - Compare student performance to class averages
   - Compare student performance to standards or benchmarks

### Query Optimization

The database schema includes indexes on:
- Primary keys for all tables
- Foreign keys for relationship navigation
- Common filter fields (student_id, academic_year, quarter)
- Composite keys for uniqueness constraints

Additional optimization considerations:
- Denormalized summary tables for faster reporting
- Materialized views for complex aggregations
- Caching for frequently accessed data

## Data Validation Rules

### Student Data

- Student ID must follow the pattern "STU" followed by 5 digits
- Grade level must be between 1 and 12
- Academic year must follow the pattern "YYYY-YYYY"

### Academic Data

- Scores must be between 0 and 100
- GPA must be between 0.0 and 4.0
- Quarter must be between 1 and 4

### Attendance Data

- Attendance rate must be between 0 and 100
- Tardiness must be non-negative
- Date must be within the academic year

### Behavioral Data

- Incident type must be "positive" or "negative"
- Date must be within the academic year
- Behavior trend must be "improving", "stable", or "declining"

### Extracurricular Data

- Hours per week must be positive
- Attendance rate must be between 0 and 100

## Next Steps

For API specifications that leverage this data model, please see the [API Specifications](./04-api-specifications.md).
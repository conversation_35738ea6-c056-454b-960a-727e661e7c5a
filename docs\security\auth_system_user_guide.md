# Authentication and Authorization System User Guide

## Introduction

This guide provides instructions for using and integrating the authentication and authorization system in the SWOT Analysis Platform. The system manages user accounts, roles, permissions, and data access controls.

## Getting Started

### Installation and Setup

1. The authentication system is included as part of the SWOT Analysis Platform code base.
2. Initialize the database with default roles and permissions:

```bash
python code/auth/initialize_auth_db.py
```

3. This will create an SQLite database with test users:
   - **Admin user**: Username: `admin`, Password: `adminpassword`
   - **Teacher user**: Username: `teacher`, Password: `teacherpassword`
   - **Parent user**: Username: `parent`, Password: `parentpassword`

### Testing the Authentication Interface

The system includes a command-line interface for testing and administrative tasks:

```bash
python code/auth/interface.py
```

This will start an interactive shell where you can:
- Log in with a user account
- Register new users
- Test permissions
- Manage user-student relationships

## User Management

### User Roles

The system defines three main user roles:

1. **Admin**: Full access to all features and data
2. **Teacher**: Access to assigned students and educational features
3. **Parent**: Limited access to their own children's data

### Registering Users

New users can be registered through the AuthService:

```python
from auth.services.auth_service import AuthService

# Register a new user
success, response = AuthService.register_user(
    username='newuser',
    email='<EMAIL>',
    password='securepassword',
    first_name='New',
    last_name='User'
)

if success:
    print(f"User registered with ID: {response['user']['id']}")
else:
    print(f"Registration failed: {response['message']}")
```

### Managing User Roles

Assigning roles to users (admin only):

```python
from auth.models.user import User
from auth.models.role import Role

# Get user and role
user = User.get_by_username('newuser')
teacher_role = Role.get_by_name('teacher')

# Update user's role
User.update_record(user['id'], {'role_id': teacher_role['id']})
```

### Password Management

Users can change their own passwords:

```python
from auth.services.auth_service import AuthService

# Change password (user must provide current password)
success, response = AuthService.change_password(
    user_id='user_id_here',
    current_password='currentpassword',
    new_password='newpassword'
)
```

Administrators can reset passwords for other users:

```python
from auth.services.auth_service import AuthService

# Reset password (admin function)
success, response = AuthService.reset_password(
    user_id='user_id_here',
    new_password='newpassword'
)
```

## Authentication

### Logging In

To authenticate a user:

```python
from auth.services.auth_service import AuthService

# Login with username/email and password
success, response = AuthService.login(
    username_or_email='username_or_email',
    password='password',
    ip_address='127.0.0.1',  # Optional
    user_agent='Browser/1.0'  # Optional
)

if success:
    user = response['user']
    session = response['session']
    token = session['token']
    # Store the token for future requests
else:
    print(f"Login failed: {response['message']}")
```

### Session Management

Validating a session token:

```python
from auth.services.auth_service import AuthService

# Validate session token
success, response = AuthService.validate_session(token)

if success:
    user = response['user']
    # User is authenticated, proceed with the request
else:
    # Session is invalid or expired
    print(f"Session validation failed: {response['message']}")
```

### Logging Out

Ending a user session:

```python
from auth.services.auth_service import AuthService

# Logout
success, response = AuthService.logout(token)

if success:
    print("Logout successful")
else:
    print(f"Logout failed: {response['message']}")
```

## Authorization

### Checking Permissions

Verifying if a user has a specific permission:

```python
from auth.services.auth_service import AuthService

# Check permission
has_permission = AuthService.check_permission(
    user_id='user_id_here',
    permission_code='view_student_details'
)

if has_permission:
    # Allow the action
    pass
else:
    # Deny the action
    pass
```

### Student Data Access Control

Checking if a user can access a student's data:

```python
from auth.services.auth_service import AuthService

# Check student access
can_access = AuthService.can_access_student_data(
    user_id='user_id_here',
    student_id='STU12345'
)

if can_access:
    # Allow access to student data
    pass
else:
    # Deny access to student data
    pass
```

Getting all accessible students for a user:

```python
from auth.services.auth_service import AuthService

# Get accessible students
student_ids = AuthService.get_accessible_students(
    user_id='user_id_here'
)

for student_id in student_ids:
    # Process each accessible student
    pass
```

### Managing Student Relationships

Linking a user to a student (admin only):

```python
from auth.models.relationship import Relationship

# Create a new relationship
relationship_id = Relationship.create_relationship(
    user_id='user_id_here',
    student_id='STU12345',
    relation_type=Relationship.TYPE_PARENT  # or TYPE_TEACHER, TYPE_ADMIN
)
```

## Middleware Integration

### Using with Web Frameworks

The authentication system includes middleware for web frameworks:

```python
# Flask example
from flask import Flask
from auth.middleware.auth_middleware import AuthMiddleware

app = Flask(__name__)

@app.route('/protected')
@AuthMiddleware.require_auth()
def protected_route():
    return "Protected content"

@app.route('/admin')
@AuthMiddleware.require_permission('manage_users')
def admin_route():
    return "Admin content"

@app.route('/student/<student_id>')
@AuthMiddleware.check_student_access('student_id')
def student_route(student_id):
    return f"Student {student_id} data"
```

## Best Practices

### Security

1. **Secure Password Storage**:
   - Always use the provided methods for password management
   - Never store plaintext passwords

2. **Session Management**:
   - Store session tokens securely (e.g., HTTP-only cookies)
   - Invalidate sessions on logout
   - Set appropriate session expiry times

3. **Access Control**:
   - Always check permissions before performing restricted actions
   - Verify student access before displaying student data

### Performance

1. **Caching**:
   - Cache permission checks for frequently accessed resources
   - Cache user-student relationships for faster access checks

2. **Database Optimization**:
   - Use the provided indexes for optimal query performance
   - Consider adding additional indexes for frequent queries

## Troubleshooting

### Common Issues

1. **Authentication Failures**:
   - Verify username/email and password
   - Check if account is locked due to failed login attempts
   - Ensure the user account is active

2. **Permission Denied**:
   - Verify user role and associated permissions
   - Check if permission is correctly assigned to the role

3. **Student Access Issues**:
   - Verify relationship between user and student
   - Check relationship type

### Debugging

The authentication system includes logging for debugging:

```python
import logging

# Enable debug logging
logging.getLogger('auth').setLevel(logging.DEBUG)
```

Logs are written to both console and log files.

## Advanced Topics

### Creating Custom Roles

Adding a new role with specific permissions:

```python
from auth.models.role import Role
from auth.models.permission import Permission

# Create a new role
role_id = Role.create_role(
    name='counselor',
    description='Student counselor with focused access',
    is_default=False
)

# Assign permissions to the role
Permission.get_by_code('view_dashboard')
Role.add_permission_to_role(role_id, dashboard_perm_id)
# Add more permissions as needed
```

### Custom Permission Checks

For complex authorization scenarios:

```python
from auth.models.user import User
from auth.models.role import Role

def check_report_access(user_id, report_type):
    """Check if a user can access a specific report type."""
    # Get user's role
    user = User.get_by_id(user_id)
    if not user:
        return False
    
    # Get role permissions
    permissions = User.get_user_permissions(user_id)
    
    # Check permissions based on report type
    if report_type == 'academic':
        return 'view_student_details' in permissions
    elif report_type == 'behavioral':
        return 'view_behavior_reports' in permissions
    elif report_type == 'administrative':
        return 'manage_users' in permissions
    
    return False
```

## API Reference

For a complete reference of all authentication and authorization functions, please refer to the class documentation in the code:

- **User Model**: `code/auth/models/user.py`
- **Role Model**: `code/auth/models/role.py`
- **Permission Model**: `code/auth/models/permission.py`
- **Relationship Model**: `code/auth/models/relationship.py`
- **Session Model**: `code/auth/models/session.py`
- **Auth Service**: `code/auth/services/auth_service.py`
- **Auth Middleware**: `code/auth/middleware/auth_middleware.py`

## Conclusion

The authentication and authorization system provides a secure foundation for the SWOT Analysis Platform. By following this guide, you can implement proper user authentication, role-based access control, and data protection in your application.
# API Specifications

This document details the RESTful API endpoints provided by the SWOT Analysis Platform, including request and response formats, authentication requirements, and usage examples.

## API Overview

The platform provides a comprehensive set of RESTful APIs organized into the following categories:

1. **Authentication API**: User authentication and session management
2. **Student API**: Student data retrieval and management
3. **SWOT Analysis API**: Generation and retrieval of SWOT analyses
4. **Visualization API**: Generation and retrieval of visualizations
5. **Data Integration API**: Importing and exporting data

All API endpoints:
- Accept and return JSON data (unless otherwise specified)
- Require authentication (except for login endpoint)
- Use HTTP status codes to indicate success/failure
- Include appropriate error messages for failed requests

## Authentication

### Authentication Header

All authenticated requests must include the following header:

```
Authorization: Bearer <jwt_token>
```

Where `<jwt_token>` is obtained from the login endpoint.

### Error Response Format

Authentication and authorization errors return the following format:

```json
{
  "success": false,
  "error": {
    "code": "invalid_token",
    "message": "Authentication token is invalid or expired"
  }
}
```

### Permissions

API endpoints require specific permissions which are tied to user roles:

| Role      | Permissions |
|-----------|-------------|
| Admin     | Full system access |
| Teacher   | View and edit assigned students, generate reports |
| Parent    | View only their children's data |

## API Endpoints

### Authentication API

#### POST /api/auth/login

Authenticates a user and returns a JWT token.

**Request:**
```json
{
  "username": "teacher1",
  "password": "securepassword123"
}
```

**Response:**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "USR12345",
    "username": "teacher1",
    "role": "teacher",
    "firstName": "John",
    "lastName": "Smith"
  }
}
```

**Status Codes:**
- 200: Success
- 401: Invalid credentials
- 422: Validation error

#### POST /api/auth/logout

Invalidates the current session token.

**Response:**
```json
{
  "success": true,
  "message": "Successfully logged out"
}
```

**Status Codes:**
- 200: Success
- 401: Unauthorized

#### GET /api/auth/me

Returns the currently authenticated user's information.

**Response:**
```json
{
  "id": "USR12345",
  "username": "teacher1",
  "role": "teacher",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Smith",
  "lastLogin": "2025-05-15T14:30:15Z"
}
```

**Status Codes:**
- 200: Success
- 401: Unauthorized

### Student API

#### GET /api/students

Returns a list of students accessible to the current user.

**Query Parameters:**
- `grade` (optional): Filter by grade level
- `homeroom` (optional): Filter by homeroom
- `limit` (optional): Limit the number of results
- `offset` (optional): Offset for pagination

**Response:**
```json
{
  "total": 120,
  "limit": 20,
  "offset": 0,
  "students": [
    {
      "id": "STU12345",
      "name": "Jane Doe",
      "gradeLevel": 9,
      "homeroom": "9A",
      "academicYear": "2024-2025",
      "gpa": 3.7,
      "attendanceRate": 95.6
    },
    // Additional students...
  ]
}
```

**Status Codes:**
- 200: Success
- 401: Unauthorized
- 403: Insufficient permissions

#### GET /api/students/:studentId

Returns detailed information about a specific student.

**Response:**
```json
{
  "id": "STU12345",
  "name": "Jane Doe",
  "gradeLevel": 9,
  "homeroom": "9A",
  "academicYear": "2024-2025",
  "contactInfo": {
    "guardians": [
      {
        "name": "John Doe",
        "relationship": "Father",
        "email": "<EMAIL>",
        "phone": "************"
      }
    ]
  },
  "summaryData": {
    "gpa": 3.7,
    "attendanceRate": 95.6,
    "behaviorPositive": 3,
    "behaviorNegative": 1,
    "behaviorTrend": "improving",
    "extracurricularCount": 2
  }
}
```

**Status Codes:**
- 200: Success
- 401: Unauthorized
- 403: Insufficient permissions
- 404: Student not found

#### GET /api/students/:studentId/academic

Returns academic data for a specific student.

**Query Parameters:**
- `year` (optional): Academic year (default: current)
- `quarter` (optional): Quarter number (default: latest)

**Response:**
```json
{
  "student_id": "STU12345",
  "academic_year": "2024-2025",
  "quarter": 1,
  "subjects": [
    {
      "subject_name": "Mathematics",
      "score": 87,
      "grade": "B+"
    },
    // Additional subjects...
  ],
  "overall_gpa": 3.7,
  "class_rank": 5,
  "class_size": 30
}
```

**Status Codes:**
- 200: Success
- 401: Unauthorized
- 403: Insufficient permissions
- 404: Student not found

#### GET /api/students/:studentId/attendance

Returns attendance data for a specific student.

**Query Parameters:**
- `year` (optional): Academic year (default: current)
- `quarter` (optional): Quarter number (default: latest)
- `detailed` (optional): Include daily records if true

**Response:**
```json
{
  "student_id": "STU12345",
  "academic_year": "2024-2025",
  "quarter": 1,
  "quarterly_summary": {
    "present_days": 43,
    "absent_days": 2,
    "tardy_days": 1,
    "attendance_rate": 95.6
  },
  "daily_records": [
    // If detailed=true
    {
      "date": "2024-09-01",
      "status": "present",
      "tardiness": 0
    },
    // Additional days...
  ]
}
```

**Status Codes:**
- 200: Success
- 401: Unauthorized
- 403: Insufficient permissions
- 404: Student not found

#### GET /api/students/:studentId/behavior

Returns behavioral data for a specific student.

**Query Parameters:**
- `year` (optional): Academic year (default: current)
- `quarter` (optional): Quarter number (default: latest)
- `detailed` (optional): Include incident details if true

**Response:**
```json
{
  "student_id": "STU12345",
  "academic_year": "2024-2025",
  "quarter": 1,
  "quarterly_summary": {
    "positive_incidents": 3,
    "negative_incidents": 1,
    "behavior_trend": "improving"
  },
  "incidents": [
    // If detailed=true
    {
      "date": "2024-09-15",
      "type": "positive",
      "description": "Helped a new student"
    },
    // Additional incidents...
  ]
}
```

**Status Codes:**
- 200: Success
- 401: Unauthorized
- 403: Insufficient permissions
- 404: Student not found

#### GET /api/students/:studentId/extracurricular

Returns extracurricular activity data for a specific student.

**Query Parameters:**
- `year` (optional): Academic year (default: current)

**Response:**
```json
{
  "student_id": "STU12345",
  "academic_year": "2024-2025",
  "activities": [
    {
      "activity_name": "Chess Club",
      "role": "Member",
      "hours_per_week": 2,
      "attendance_rate": 100,
      "advisor": "Mr. Johnson"
    },
    // Additional activities...
  ],
  "total_extracurricular_hours": 5
}
```

**Status Codes:**
- 200: Success
- 401: Unauthorized
- 403: Insufficient permissions
- 404: Student not found

### SWOT Analysis API

#### GET /api/students/:studentId/swot

Returns the SWOT analysis for a specific student.

**Query Parameters:**
- `year` (optional): Academic year (default: current)
- `quarter` (optional): Quarter number (default: latest)
- `regenerate` (optional): Force regeneration if true

**Response:**
```json
{
  "student_id": "STU12345",
  "name": "Jane Doe",
  "grade_level": 9,
  "academic_year": "2024-2025",
  "quarter": 1,
  "analysis_date": "2024-10-15",
  "strengths": [
    {
      "category": "Academic",
      "type": "Subject Strength",
      "description": "Excellence in Science (A, 95%)",
      "score": 95
    },
    // Additional strengths...
  ],
  "weaknesses": [
    {
      "category": "Academic",
      "type": "Subject Weakness",
      "description": "Needs improvement in Physical Education (C-, 72%)",
      "score": 72
    },
    // Additional weaknesses...
  ],
  "opportunities": [
    {
      "category": "Academic",
      "type": "Enrichment",
      "description": "Consider Science Club or science fair participation to develop strength in Science"
    },
    // Additional opportunities...
  ],
  "threats": [
    {
      "category": "Attendance",
      "type": "Inconsistency",
      "description": "Occasional attendance issues may impact academic performance if they continue"
    },
    // Additional threats...
  ]
}
```

**Status Codes:**
- 200: Success
- 401: Unauthorized
- 403: Insufficient permissions
- 404: Student not found
- 422: Analysis generation failed

#### GET /api/students/:studentId/swot/trends

Returns trend analysis of SWOT factors over time.

**Query Parameters:**
- `year` (optional): Academic year (default: current)
- `periods` (optional): Number of periods to include (default: 4)

**Response:**
```json
{
  "student_id": "STU12345",
  "name": "Jane Doe",
  "trend_periods": [
    "2024-2025 Q1",
    "2023-2024 Q4",
    "2023-2024 Q3",
    "2023-2024 Q2"
  ],
  "strength_counts": [8, 7, 5, 4],
  "weakness_counts": [2, 3, 4, 5],
  "opportunity_counts": [7, 6, 6, 5],
  "threat_counts": [0, 1, 2, 2],
  "overall_trend": "improving",
  "category_trends": {
    "Academic": "improving",
    "Attendance": "stable",
    "Behavior": "improving",
    "Extracurricular": "stable"
  }
}
```

**Status Codes:**
- 200: Success
- 401: Unauthorized
- 403: Insufficient permissions
- 404: Student not found
- 422: Analysis generation failed

#### GET /api/classes/:classId/swot

Returns an aggregated SWOT analysis for an entire class.

**Query Parameters:**
- `year` (optional): Academic year (default: current)
- `quarter` (optional): Quarter number (default: latest)

**Response:**
```json
{
  "class_id": "9A",
  "grade_level": 9,
  "academic_year": "2024-2025",
  "quarter": 1,
  "analysis_date": "2024-10-15",
  "student_count": 25,
  "common_strengths": [
    {
      "category": "Academic",
      "type": "Subject Strength",
      "description": "Excellence in Mathematics",
      "count": 15,
      "percentage": 60
    },
    // Additional common strengths...
  ],
  "common_weaknesses": [
    {
      "category": "Academic",
      "type": "Subject Weakness",
      "description": "Difficulties in English",
      "count": 12,
      "percentage": 48
    },
    // Additional common weaknesses...
  ],
  "recommended_interventions": [
    {
      "category": "Academic",
      "type": "Group Support",
      "description": "English reading group for struggling students",
      "target_count": 12
    },
    // Additional interventions...
  ]
}
```

**Status Codes:**
- 200: Success
- 401: Unauthorized
- 403: Insufficient permissions
- 404: Class not found
- 422: Analysis generation failed

### Visualization API

#### GET /api/students/:studentId/visualizations/swot

Returns a visualization of a student's SWOT analysis.

**Query Parameters:**
- `year` (optional): Academic year (default: current)
- `quarter` (optional): Quarter number (default: latest)
- `format` (optional): Output format (png, jpg, svg, pdf) (default: png)

**Response:**
```
Binary image data with appropriate Content-Type header
```

**Status Codes:**
- 200: Success
- 401: Unauthorized
- 403: Insufficient permissions
- 404: Student not found
- 422: Visualization generation failed

#### GET /api/students/:studentId/visualizations/academic

Returns a visualization of a student's academic performance.

**Query Parameters:**
- `year` (optional): Academic year (default: current)
- `quarter` (optional): Quarter number (default: latest)
- `compare` (optional): Compare to class average if true
- `type` (optional): Visualization type (radar, bar, line) (default: radar)
- `format` (optional): Output format (png, jpg, svg, pdf) (default: png)

**Response:**
```
Binary image data with appropriate Content-Type header
```

**Status Codes:**
- 200: Success
- 401: Unauthorized
- 403: Insufficient permissions
- 404: Student not found
- 422: Visualization generation failed

#### GET /api/students/:studentId/visualizations/attendance

Returns a visualization of a student's attendance.

**Query Parameters:**
- `year` (optional): Academic year (default: current)
- `quarter` (optional): Quarter number (default: latest)
- `type` (optional): Visualization type (heatmap, trend) (default: heatmap)
- `format` (optional): Output format (png, jpg, svg, pdf) (default: png)

**Response:**
```
Binary image data with appropriate Content-Type header
```

**Status Codes:**
- 200: Success
- 401: Unauthorized
- 403: Insufficient permissions
- 404: Student not found
- 422: Visualization generation failed

#### GET /api/students/:studentId/visualizations/behavior

Returns a visualization of a student's behavioral incidents.

**Query Parameters:**
- `year` (optional): Academic year (default: current)
- `quarter` (optional): Quarter number (default: latest)
- `type` (optional): Visualization type (timeline, summary) (default: timeline)
- `format` (optional): Output format (png, jpg, svg, pdf) (default: png)

**Response:**
```
Binary image data with appropriate Content-Type header
```

**Status Codes:**
- 200: Success
- 401: Unauthorized
- 403: Insufficient permissions
- 404: Student not found
- 422: Visualization generation failed

#### GET /api/students/:studentId/visualizations/trends

Returns a visualization of a student's performance trends over time.

**Query Parameters:**
- `metric` (optional): Performance metric (gpa, attendance, behavior) (default: gpa)
- `periods` (optional): Number of periods to include (default: 4)
- `format` (optional): Output format (png, jpg, svg, pdf) (default: png)

**Response:**
```
Binary image data with appropriate Content-Type header
```

**Status Codes:**
- 200: Success
- 401: Unauthorized
- 403: Insufficient permissions
- 404: Student not found
- 422: Visualization generation failed

#### GET /api/students/:studentId/dashboard

Returns a complete student dashboard with multiple visualizations.

**Query Parameters:**
- `year` (optional): Academic year (default: current)
- `quarter` (optional): Quarter number (default: latest)
- `format` (optional): Output format (html, pdf) (default: html)

**Response:**
```
HTML document or PDF document with appropriate Content-Type header
```

**Status Codes:**
- 200: Success
- 401: Unauthorized
- 403: Insufficient permissions
- 404: Student not found
- 422: Dashboard generation failed

### Data Integration API

#### POST /api/import/students

Imports student profile data.

**Request:**
```json
{
  "students": [
    {
      "student_id": "STU12345",
      "name": "Jane Doe",
      "grade_level": 9,
      "homeroom": "9A",
      "academic_year": "2024-2025"
    },
    // Additional students...
  ]
}
```

**Response:**
```json
{
  "success": true,
  "imported": 25,
  "errors": [],
  "warnings": []
}
```

**Status Codes:**
- 200: Success
- 401: Unauthorized
- 403: Insufficient permissions
- 422: Validation errors

#### POST /api/import/academic

Imports academic performance data.

**Request:**
```json
{
  "academic_data": [
    {
      "student_id": "STU12345",
      "academic_year": "2024-2025",
      "quarter": 1,
      "subjects": [
        {
          "subject_name": "Mathematics",
          "score": 87,
          "grade": "B+"
        },
        // Additional subjects...
      ],
      "overall_gpa": 3.7
    },
    // Additional student records...
  ]
}
```

**Response:**
```json
{
  "success": true,
  "imported": 25,
  "errors": [],
  "warnings": []
}
```

**Status Codes:**
- 200: Success
- 401: Unauthorized
- 403: Insufficient permissions
- 422: Validation errors

#### POST /api/import/attendance

Imports attendance data.

**Request:**
```json
{
  "attendance_data": [
    {
      "student_id": "STU12345",
      "academic_year": "2024-2025",
      "quarter": 1,
      "daily_records": [
        {
          "date": "2024-09-01",
          "status": "present",
          "tardiness": 0
        },
        // Additional daily records...
      ]
    },
    // Additional student records...
  ]
}
```

**Response:**
```json
{
  "success": true,
  "imported": 25,
  "errors": [],
  "warnings": []
}
```

**Status Codes:**
- 200: Success
- 401: Unauthorized
- 403: Insufficient permissions
- 422: Validation errors

#### GET /api/export/students/:studentId

Exports all data for a specific student.

**Query Parameters:**
- `year` (optional): Academic year (default: current)
- `format` (optional): Output format (json, csv, pdf) (default: json)

**Response:**
```
JSON, CSV, or PDF data with appropriate Content-Type header
```

**Status Codes:**
- 200: Success
- 401: Unauthorized
- 403: Insufficient permissions
- 404: Student not found
- 422: Export generation failed

#### GET /api/export/class/:classId

Exports all data for an entire class.

**Query Parameters:**
- `year` (optional): Academic year (default: current)
- `quarter` (optional): Quarter number (default: latest)
- `format` (optional): Output format (json, csv, pdf) (default: json)

**Response:**
```
JSON, CSV, or PDF data with appropriate Content-Type header
```

**Status Codes:**
- 200: Success
- 401: Unauthorized
- 403: Insufficient permissions
- 404: Class not found
- 422: Export generation failed

## Error Handling

### Error Response Format

All error responses follow this format:

```json
{
  "success": false,
  "error": {
    "code": "error_code",
    "message": "Human-readable error message",
    "details": {
      // Optional additional error details
    }
  }
}
```

### Common Error Codes

| Code | Description |
|------|-------------|
| `invalid_credentials` | Username or password is incorrect |
| `invalid_token` | Authentication token is invalid or expired |
| `insufficient_permissions` | User lacks required permissions |
| `resource_not_found` | Requested resource does not exist |
| `validation_error` | Request data failed validation |
| `server_error` | Internal server error |

## Rate Limiting

API requests are subject to rate limiting to prevent abuse:

- 60 requests per minute for standard users
- 300 requests per minute for administrative users

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 58
X-RateLimit-Reset: 1589547834
```

When rate limit is exceeded, a 429 (Too Many Requests) status code is returned.

## API Versioning

API versioning is supported through URL path versioning:

```
/api/v1/students
```

And through Accept header versioning:

```
Accept: application/vnd.swot.v1+json
```

## Documentation

OpenAPI/Swagger documentation is available at:

```
/api/docs
```

## Webhook and Real-Time Update Functionality

The SWOT Analysis Platform provides webhook capabilities to enable real-time data updates and integration with external systems.

### Webhook Registration

#### POST /api/webhooks/register

Registers a new webhook endpoint to receive notifications about specific events.

**Request:**
```json
{
  "callbackUrl": "https://example.com/api/swot-updates",
  "events": [
    "student.data.updated",
    "swot.analysis.completed",
    "attendance.updated",
    "behavior.incident.created"
  ],
  "secret": "your_webhook_secret",
  "description": "School SIS Integration"
}
```

**Response:**
```json
{
  "success": true,
  "webhookId": "WHK78901",
  "events": [
    "student.data.updated",
    "swot.analysis.completed",
    "attendance.updated",
    "behavior.incident.created"
  ],
  "status": "active"
}
```

#### GET /api/webhooks

Lists all registered webhooks for the authenticated user or organization.

**Response:**
```json
{
  "success": true,
  "webhooks": [
    {
      "webhookId": "WHK78901",
      "callbackUrl": "https://example.com/api/swot-updates",
      "events": [
        "student.data.updated",
        "swot.analysis.completed"
      ],
      "status": "active",
      "createdAt": "2025-04-10T09:15:23Z",
      "lastTriggered": "2025-05-15T14:22:10Z"
    }
  ]
}
```

#### DELETE /api/webhooks/:webhookId

Deletes a registered webhook.

**Response:**
```json
{
  "success": true,
  "message": "Webhook WHK78901 successfully deleted"
}
```

### Webhook Payload Format

When an event occurs that triggers a webhook, the platform sends a POST request to the registered callback URL with the following payload structure:

```json
{
  "eventId": "EVT123456",
  "eventType": "student.data.updated",
  "timestamp": "2025-05-16T15:32:10Z",
  "resource": {
    "type": "student",
    "id": "STU12345"
  },
  "data": {
    // Event-specific data
  },
  "signature": "sha256=..."
}
```

### Webhook Event Types

The platform supports the following webhook event types:

| Event Type | Description |
|------------|-------------|
| `student.data.updated` | Triggered when any student data is updated |
| `academic.grade.added` | Triggered when a new grade is recorded |
| `attendance.updated` | Triggered when attendance records are updated |
| `behavior.incident.created` | Triggered when a new behavioral incident is recorded |
| `swot.analysis.completed` | Triggered when a SWOT analysis is generated |
| `report.generated` | Triggered when a report is generated |

### Webhook Security

To verify that webhook requests are coming from the SWOT Analysis Platform:

1. A `X-SWOT-Signature` header is included with each webhook request
2. The signature is generated using HMAC with SHA-256, using your webhook secret
3. Verify the signature by computing an HMAC with your secret and the request body
4. Compare your computed signature with the one in the header

Example signature verification (pseudocode):
```
signatureHeader = request.headers["X-SWOT-Signature"]
payload = request.body
expectedSignature = HMAC_SHA256(webhookSecret, payload)
isValid = (signatureHeader == expectedSignature)
```

## Integration Examples

This section provides examples of integrating the SWOT Analysis Platform with common education systems.

### School Information System (SIS) Integration

#### Student Data Synchronization

```python
import requests
import json

# Configuration
API_BASE_URL = "https://swot-platform.example.edu/api"
API_KEY = "your_api_key"

def sync_students_from_sis():
    # Get students from SIS
    sis_students = get_students_from_sis()
    
    # Format for SWOT platform
    swot_students = []
    for student in sis_students:
        swot_students.append({
            "studentId": student["id"],
            "firstName": student["first_name"],
            "lastName": student["last_name"],
            "grade": student["grade_level"],
            "classId": student["homeroom_id"]
        })
    
    # Import to SWOT platform
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    response = requests.post(
        f"{API_BASE_URL}/api/import/students",
        headers=headers,
        data=json.dumps({"students": swot_students})
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"Successfully imported {result['imported']} students")
    else:
        print(f"Error: {response.status_code} - {response.text}")

# Call the function
sync_students_from_sis()
```

#### Real-time Grade Updates via Webhook

```javascript
// Express.js webhook handler example
const express = require('express');
const crypto = require('crypto');
const bodyParser = require('body-parser');

const app = express();
app.use(bodyParser.json());

const WEBHOOK_SECRET = 'your_webhook_secret';

// Webhook handler for grade updates
app.post('/api/swot-updates', (req, res) => {
  // Verify webhook signature
  const signature = req.headers['x-swot-signature'];
  const payload = JSON.stringify(req.body);
  const hmac = crypto.createHmac('sha256', WEBHOOK_SECRET);
  const digest = 'sha256=' + hmac.update(payload).digest('hex');
  
  if (signature !== digest) {
    return res.status(401).send('Invalid signature');
  }
  
  // Process the webhook data
  const event = req.body;
  if (event.eventType === 'academic.grade.added') {
    console.log(`New grade for student ${event.resource.id}: ${event.data.subject} - ${event.data.score}`);
    
    // Update your SIS or LMS with this grade information
    updateGradeInSIS(event.resource.id, event.data.subject, event.data.score);
  }
  
  res.status(200).send('Webhook received');
});

function updateGradeInSIS(studentId, subject, score) {
  // Implementation to update your SIS
  console.log(`Updating grade in SIS for ${studentId}: ${subject} - ${score}`);
}

app.listen(3000, () => {
  console.log('Webhook server listening on port 3000');
});
```

### Gradebook Integration

The following example demonstrates pulling grades from a third-party gradebook system into the SWOT platform:

```python
import requests
import json
import pandas as pd
from datetime import datetime

# Configuration
GRADEBOOK_API_URL = "https://gradebook.example.edu/api"
GRADEBOOK_API_KEY = "your_gradebook_api_key"
SWOT_API_URL = "https://swot-platform.example.edu/api"
SWOT_API_KEY = "your_swot_api_key"

def sync_gradebook_scores():
    # Get current quarter info
    current_date = datetime.now()
    academic_year = f"{current_date.year}-{current_date.year + 1}" if current_date.month > 7 else f"{current_date.year - 1}-{current_date.year}"
    quarter = determine_current_quarter(current_date)
    
    # Get grades from gradebook system
    headers_gradebook = {
        "Authorization": f"Bearer {GRADEBOOK_API_KEY}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(
        f"{GRADEBOOK_API_URL}/grades",
        headers=headers_gradebook,
        params={"quarter": quarter, "year": academic_year}
    )
    
    if response.status_code != 200:
        print(f"Error fetching grades: {response.status_code} - {response.text}")
        return
    
    gradebook_data = response.json()
    
    # Transform data for SWOT platform
    swot_academic_data = []
    for student in gradebook_data["students"]:
        student_grades = {
            "studentId": student["id"],
            "quarter": quarter,
            "academicYear": academic_year,
            "subjects": []
        }
        
        for subject, grades in student["subjects"].items():
            subject_data = {
                "name": subject,
                "score": calculate_average(grades["assignments"]),
                "grade": convert_to_letter_grade(calculate_average(grades["assignments"])),
                "assignments": []
            }
            
            for assignment in grades["assignments"]:
                subject_data["assignments"].append({
                    "name": assignment["name"],
                    "score": assignment["score"],
                    "maxScore": assignment["possible_points"],
                    "date": assignment["date"]
                })
            
            student_grades["subjects"].append(subject_data)
        
        swot_academic_data.append(student_grades)
    
    # Import to SWOT platform
    headers_swot = {
        "Authorization": f"Bearer {SWOT_API_KEY}",
        "Content-Type": "application/json"
    }
    
    response = requests.post(
        f"{SWOT_API_URL}/api/import/academic",
        headers=headers_swot,
        data=json.dumps({"students": swot_academic_data})
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"Successfully imported academic data for {result['imported']} students")
    else:
        print(f"Error: {response.status_code} - {response.text}")

def calculate_average(assignments):
    total_points = sum(assignment["score"] for assignment in assignments)
    total_possible = sum(assignment["possible_points"] for assignment in assignments)
    return (total_points / total_possible) * 100 if total_possible > 0 else 0

def convert_to_letter_grade(score):
    if score >= 90: return "A"
    elif score >= 80: return "B"
    elif score >= 70: return "C"
    elif score >= 60: return "D"
    else: return "F"

def determine_current_quarter(date):
    month = date.month
    if month >= 8 and month <= 10: return "Q1"
    elif month >= 11 or month == 1: return "Q2"
    elif month >= 2 and month <= 4: return "Q3"
    else: return "Q4"

# Call the function
sync_gradebook_scores()
```

### Attendance System Integration

Example of importing attendance data from an external attendance system:

```python
import requests
import json
import pandas as pd
from datetime import datetime, timedelta

# Configuration
ATTENDANCE_API_URL = "https://attendance.example.edu/api"
ATTENDANCE_API_KEY = "your_attendance_api_key"
SWOT_API_URL = "https://swot-platform.example.edu/api"
SWOT_API_KEY = "your_swot_api_key"

def sync_attendance_data():
    # Calculate date range for current quarter
    current_date = datetime.now()
    start_date, end_date = get_quarter_date_range(current_date)
    
    # Get attendance data from attendance system
    headers_attendance = {
        "Authorization": f"Bearer {ATTENDANCE_API_KEY}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(
        f"{ATTENDANCE_API_URL}/attendance",
        headers=headers_attendance,
        params={
            "start_date": start_date.strftime("%Y-%m-%d"),
            "end_date": end_date.strftime("%Y-%m-%d")
        }
    )
    
    if response.status_code != 200:
        print(f"Error fetching attendance data: {response.status_code} - {response.text}")
        return
    
    attendance_data = response.json()
    
    # Transform data for SWOT platform
    swot_attendance_data = []
    for student in attendance_data["students"]:
        student_attendance = {
            "studentId": student["id"],
            "academicYear": f"{current_date.year}-{current_date.year + 1}" if current_date.month > 7 else f"{current_date.year - 1}-{current_date.year}",
            "quarter": get_current_quarter(current_date),
            "records": []
        }
        
        for date, status in student["attendance"].items():
            student_attendance["records"].append({
                "date": date,
                "status": map_attendance_status(status),
                "isExcused": status.get("excused", False),
                "notes": status.get("notes", "")
            })
        
        swot_attendance_data.append(student_attendance)
    
    # Import to SWOT platform
    headers_swot = {
        "Authorization": f"Bearer {SWOT_API_KEY}",
        "Content-Type": "application/json"
    }
    
    response = requests.post(
        f"{SWOT_API_URL}/api/import/attendance",
        headers=headers_swot,
        data=json.dumps({"students": swot_attendance_data})
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"Successfully imported attendance data for {result['imported']} students")
    else:
        print(f"Error: {response.status_code} - {response.text}")

def map_attendance_status(status):
    # Maps external attendance codes to SWOT platform codes
    status_code = status.get("code", "").lower()
    if status_code in ["p", "present"]:
        return "present"
    elif status_code in ["t", "tardy"]:
        return "tardy"
    elif status_code in ["a", "absent"]:
        return "absent"
    else:
        return "unknown"

def get_current_quarter(date):
    month = date.month
    if month >= 8 and month <= 10: return "Q1"
    elif month >= 11 or month == 1: return "Q2"
    elif month >= 2 and month <= 4: return "Q3"
    else: return "Q4"

def get_quarter_date_range(date):
    quarter = get_current_quarter(date)
    year = date.year
    
    if quarter == "Q1":
        return datetime(year, 8, 1), datetime(year, 10, 31)
    elif quarter == "Q2":
        if date.month == 1:
            return datetime(year-1, 11, 1), datetime(year, 1, 31)
        else:
            return datetime(year, 11, 1), datetime(year+1, 1, 31)
    elif quarter == "Q3":
        return datetime(year, 2, 1), datetime(year, 4, 30)
    else:  # Q4
        return datetime(year, 5, 1), datetime(year, 7, 31)

# Call the function
sync_attendance_data()
```

### Learning Management System (LMS) Integration

Example of integrating with a Learning Management System using the SWOT API:

```javascript
// Using Node.js with axios
const axios = require('axios');

// Configuration
const LMS_API_URL = 'https://lms.example.edu/api';
const LMS_API_KEY = 'your_lms_api_key';
const SWOT_API_URL = 'https://swot-platform.example.edu/api';
const SWOT_API_KEY = 'your_swot_api_key';

// Function to pull SWOT analysis data and display in LMS
async function displaySWOTInLMS(studentId) {
  try {
    // Get SWOT analysis from SWOT platform
    const response = await axios.get(
      `${SWOT_API_URL}/api/students/${studentId}/swot/latest`,
      {
        headers: {
          'Authorization': `Bearer ${SWOT_API_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    if (response.status !== 200) {
      console.error(`Error fetching SWOT data: ${response.status}`);
      return null;
    }
    
    const swotData = response.data;
    
    // Format data for LMS display
    const lmsWidgetData = {
      title: `SWOT Analysis for Student ${studentId}`,
      lastUpdated: swotData.generatedAt,
      sections: [
        {
          title: 'Strengths',
          items: swotData.strengths.map(strength => ({
            text: strength.description,
            category: strength.category,
            value: strength.value || null
          }))
        },
        {
          title: 'Weaknesses',
          items: swotData.weaknesses.map(weakness => ({
            text: weakness.description,
            category: weakness.category,
            value: weakness.value || null
          }))
        },
        {
          title: 'Opportunities',
          items: swotData.opportunities.map(opportunity => ({
            text: opportunity.description,
            category: opportunity.category
          }))
        },
        {
          title: 'Threats',
          items: swotData.threats.map(threat => ({
            text: threat.description,
            category: threat.category,
            urgency: threat.urgency || 'medium'
          }))
        }
      ],
      recommendations: swotData.recommendations || []
    };
    
    // Push to LMS
    const lmsResponse = await axios.post(
      `${LMS_API_URL}/students/${studentId}/widgets`,
      {
        widgetType: 'swot_analysis',
        data: lmsWidgetData,
        position: 'sidebar',
        visibleTo: ['teacher', 'advisor', 'administrator']
      },
      {
        headers: {
          'Authorization': `Bearer ${LMS_API_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    if (lmsResponse.status === 200 || lmsResponse.status === 201) {
      console.log(`Successfully added SWOT widget to LMS for student ${studentId}`);
      return lmsResponse.data.widgetId;
    } else {
      console.error(`Error pushing to LMS: ${lmsResponse.status}`);
      return null;
    }
  } catch (error) {
    console.error(`Exception during LMS integration: ${error.message}`);
    return null;
  }
}

// Example usage
displaySWOTInLMS('STU12345')
  .then(widgetId => {
    if (widgetId) {
      console.log(`Widget created with ID: ${widgetId}`);
    }
  })
  .catch(error => {
    console.error(`Integration failed: ${error.message}`);
  });
```

## Mobile Application Integration

Example of integrating the SWOT Analysis Platform with a mobile application:

```swift
// Swift example for iOS app
import Foundation

class SWOTApiClient {
    private let baseUrl = "https://swot-platform.example.edu/api"
    private let apiKey: String
    
    init(apiKey: String) {
        self.apiKey = apiKey
    }
    
    func fetchStudentSWOT(studentId: String, completion: @escaping (Result<SWOTAnalysis, Error>) -> Void) {
        guard let url = URL(string: "\(baseUrl)/api/students/\(studentId)/swot/latest") else {
            completion(.failure(NSError(domain: "SWOTApiClient", code: 1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])))
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.addValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse,
                  (200...299).contains(httpResponse.statusCode) else {
                completion(.failure(NSError(domain: "SWOTApiClient", code: 2, userInfo: [NSLocalizedDescriptionKey: "Invalid response"])))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "SWOTApiClient", code: 3, userInfo: [NSLocalizedDescriptionKey: "No data received"])))
                return
            }
            
            do {
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                let swotAnalysis = try decoder.decode(SWOTAnalysis.self, from: data)
                completion(.success(swotAnalysis))
            } catch {
                completion(.failure(error))
            }
        }
        
        task.resume()
    }
}

// Model definitions
struct SWOTAnalysis: Decodable {
    let studentId: String
    let generatedAt: Date
    let strengths: [SWOTItem]
    let weaknesses: [SWOTItem]
    let opportunities: [SWOTItem]
    let threats: [SWOTItem]
    let recommendations: [Recommendation]?
}

struct SWOTItem: Decodable {
    let id: String
    let description: String
    let category: String
    let value: Double?
    let urgency: String?
}

struct Recommendation: Decodable {
    let id: String
    let text: String
    let type: String
    let priority: String
}

// Usage example
let client = SWOTApiClient(apiKey: "your_api_key")
client.fetchStudentSWOT(studentId: "STU12345") { result in
    switch result {
    case .success(let swot):
        DispatchQueue.main.async {
            // Update UI with SWOT data
            print("SWOT analysis received for student \(swot.studentId)")
            print("Strengths: \(swot.strengths.count)")
            print("Weaknesses: \(swot.weaknesses.count)")
            print("Opportunities: \(swot.opportunities.count)")
            print("Threats: \(swot.threats.count)")
        }
    case .failure(let error):
        DispatchQueue.main.async {
            // Handle error
            print("Error fetching SWOT analysis: \(error.localizedDescription)")
        }
    }
}
```

## Next Steps

For implementation guidelines, please see the [Implementation Guidelines](./05-implementation-guidelines.md).
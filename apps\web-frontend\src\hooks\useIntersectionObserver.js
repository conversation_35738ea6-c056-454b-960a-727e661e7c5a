import { useEffect, useRef, useState } from 'react';

/**
 * Custom hook for Intersection Observer API
 * Optimized for performance and mobile devices
 */
export const useIntersectionObserver = ({
  threshold = 0.1,
  root = null,
  rootMargin = '0px',
  once = true,
  enabled = true,
}) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasIntersected, setHasIntersected] = useState(false);
  const targetRef = useRef(null);
  const observerRef = useRef(null);

  useEffect(() => {
    if (!enabled) return;

    const target = targetRef.current;
    if (!target) return;

    // Check if IntersectionObserver is supported
    if (!window.IntersectionObserver) {
      // Fallback for older browsers - assume visible
      setIsIntersecting(true);
      setHasIntersected(true);
      return;
    }

    const handleIntersection = (entries) => {
      const [entry] = entries;
      const isCurrentlyIntersecting = entry.isIntersecting;
      
      setIsIntersecting(isCurrentlyIntersecting);
      
      if (isCurrentlyIntersecting && !hasIntersected) {
        setHasIntersected(true);
        
        // If 'once' is true, disconnect after first intersection
        if (once && observerRef.current) {
          observerRef.current.disconnect();
        }
      }
    };

    // Create observer with optimized options
    observerRef.current = new IntersectionObserver(handleIntersection, {
      threshold: Array.isArray(threshold) ? threshold : [threshold],
      root,
      rootMargin,
    });

    observerRef.current.observe(target);

    // Cleanup function
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [threshold, root, rootMargin, once, enabled, hasIntersected]);

  // Return the ref and intersection state
  return [targetRef, once ? hasIntersected : isIntersecting, hasIntersected];
};

/**
 * Hook for lazy loading images with intersection observer
 */
export const useLazyImage = (src, options = {}) => {
  const {
    placeholder = '',
    threshold = 0.1,
    rootMargin = '50px',
  } = options;

  const [imageSrc, setImageSrc] = useState(placeholder);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [ref, isIntersecting] = useIntersectionObserver({
    threshold,
    rootMargin,
    once: true,
  });

  useEffect(() => {
    if (isIntersecting && src && !imageLoaded && !imageError) {
      const img = new Image();
      
      img.onload = () => {
        setImageSrc(src);
        setImageLoaded(true);
      };
      
      img.onerror = () => {
        setImageError(true);
      };
      
      img.src = src;
    }
  }, [isIntersecting, src, imageLoaded, imageError]);

  return {
    ref,
    src: imageSrc,
    isLoaded: imageLoaded,
    isError: imageError,
    isIntersecting,
  };
};

/**
 * Hook for infinite scrolling with intersection observer
 */
export const useInfiniteScroll = (callback, options = {}) => {
  const {
    threshold = 1.0,
    rootMargin = '0px',
    enabled = true,
  } = options;

  const [isFetching, setIsFetching] = useState(false);
  const [ref, isIntersecting] = useIntersectionObserver({
    threshold,
    rootMargin,
    once: false,
    enabled: enabled && !isFetching,
  });

  useEffect(() => {
    if (isIntersecting && enabled && !isFetching) {
      setIsFetching(true);
      
      Promise.resolve(callback())
        .finally(() => {
          setIsFetching(false);
        });
    }
  }, [isIntersecting, enabled, isFetching, callback]);

  return {
    ref,
    isFetching,
    isIntersecting,
  };
};

/**
 * Hook for tracking element visibility for analytics
 */
export const useVisibilityTracking = (callback, options = {}) => {
  const {
    threshold = 0.5,
    rootMargin = '0px',
    minVisibleTime = 1000, // Minimum time in ms to consider as "viewed"
  } = options;

  const [ref, isIntersecting] = useIntersectionObserver({
    threshold,
    rootMargin,
    once: false,
  });

  const visibilityStartTime = useRef(null);
  const hasBeenTracked = useRef(false);

  useEffect(() => {
    if (isIntersecting) {
      // Element became visible
      if (!visibilityStartTime.current) {
        visibilityStartTime.current = Date.now();
      }
    } else {
      // Element became hidden
      if (visibilityStartTime.current && !hasBeenTracked.current) {
        const visibleDuration = Date.now() - visibilityStartTime.current;
        
        if (visibleDuration >= minVisibleTime) {
          callback({
            duration: visibleDuration,
            timestamp: Date.now(),
          });
          hasBeenTracked.current = true;
        }
        
        visibilityStartTime.current = null;
      }
    }
  }, [isIntersecting, callback, minVisibleTime]);

  return {
    ref,
    isVisible: isIntersecting,
    hasBeenTracked: hasBeenTracked.current,
  };
};

/**
 * Hook for progressive content loading
 */
export const useProgressiveLoading = (items, options = {}) => {
  const {
    batchSize = 10,
    threshold = 0.8,
    rootMargin = '100px',
  } = options;

  const [visibleItems, setVisibleItems] = useState(
    items.slice(0, Math.min(batchSize, items.length))
  );
  const [hasMore, setHasMore] = useState(items.length > batchSize);

  const loadMore = () => {
    const currentLength = visibleItems.length;
    const nextBatch = items.slice(currentLength, currentLength + batchSize);
    
    setVisibleItems(prev => [...prev, ...nextBatch]);
    setHasMore(currentLength + batchSize < items.length);
  };

  const { ref, isFetching } = useInfiniteScroll(loadMore, {
    threshold,
    rootMargin,
    enabled: hasMore,
  });

  // Update visible items when items prop changes
  useEffect(() => {
    const newVisibleCount = Math.min(visibleItems.length, items.length);
    setVisibleItems(items.slice(0, Math.max(newVisibleCount, batchSize)));
    setHasMore(items.length > newVisibleCount);
  }, [items, batchSize, visibleItems.length]);

  return {
    visibleItems,
    hasMore,
    isLoading: isFetching,
    loadMoreRef: ref,
  };
};

export default useIntersectionObserver;

# VidyaMitra Design System
## Comprehensive UI/UX Guidelines for Indian Educational Platform

### Overview
VidyaMitra's design system is specifically crafted for the Indian education market, incorporating cultural preferences, accessibility requirements, and mobile-first design principles to create an engaging and intuitive experience for teachers, parents, and administrators.

## Core Design Principles

### 1. Cultural Sensitivity
- **Color Psychology**: Leverage Indian cultural color associations (saffron for knowledge, green for prosperity)
- **Visual Hierarchy**: Respect traditional information organization patterns
- **Iconography**: Use culturally relevant symbols and metaphors
- **Typography**: Support for multiple Indic scripts with proper rendering

### 2. Accessibility First
- **WCAG 2.1 AA Compliance**: Ensure accessibility for diverse user abilities
- **High Contrast**: Support for varying display qualities common in Indian schools
- **Screen Reader Support**: Comprehensive ARIA labels and semantic HTML
- **Keyboard Navigation**: Full functionality without mouse dependency

### 3. Mobile-First Approach
- **Progressive Enhancement**: Start with mobile design, enhance for desktop
- **Touch-Friendly**: Minimum 44px touch targets
- **Data Efficiency**: Optimized for varying internet speeds
- **Offline Capability**: Core functionality available without internet

### 4. Performance Optimization
- **Fast Loading**: Sub-3 second initial load times
- **Efficient Rendering**: Optimized for low-end devices
- **Progressive Loading**: Critical content first, enhancements later
- **Caching Strategy**: Intelligent caching for frequently accessed data

## Color System

### Primary Palette
```css
:root {
  /* Primary Colors - Knowledge & Trust */
  --primary-50: #E3F2FD;
  --primary-100: #BBDEFB;
  --primary-500: #2E5BA8;  /* Main brand color */
  --primary-700: #1976D2;
  --primary-900: #0D47A1;

  /* Secondary Colors - Cultural Significance */
  --secondary-50: #FFF3E0;
  --secondary-100: #FFE0B2;
  --secondary-500: #FF9933;  /* Saffron */
  --secondary-700: #F57C00;
  --secondary-900: #E65100;

  /* Success Colors - Prosperity */
  --success-50: #E8F5E8;
  --success-100: #C8E6C9;
  --success-500: #138808;   /* Indian flag green */
  --success-700: #388E3C;
  --success-900: #1B5E20;

  /* Warning Colors - Attention */
  --warning-50: #FFFBF0;
  --warning-100: #FFF4E0;
  --warning-500: #F4C430;   /* Mustard yellow */
  --warning-700: #F9A825;
  --warning-900: #F57F17;

  /* Error Colors - Alert */
  --error-50: #FFEBEE;
  --error-100: #FFCDD2;
  --error-500: #D81159;     /* Vermilion */
  --error-700: #D32F2F;
  --error-900: #B71C1C;

  /* Neutral Colors */
  --neutral-50: #FAFAFA;
  --neutral-100: #F5F5F5;
  --neutral-200: #EEEEEE;
  --neutral-300: #E0E0E0;
  --neutral-400: #BDBDBD;
  --neutral-500: #9E9E9E;
  --neutral-600: #757575;
  --neutral-700: #616161;
  --neutral-800: #424242;
  --neutral-900: #212121;
}
```

### Semantic Color Usage
- **Primary**: Navigation, CTAs, links, active states
- **Secondary**: Highlights, badges, secondary actions
- **Success**: Positive feedback, achievements, good performance
- **Warning**: Caution, moderate concerns, pending states
- **Error**: Errors, critical issues, poor performance
- **Neutral**: Text, borders, backgrounds, disabled states

## Typography System

### Font Families
```css
:root {
  /* Primary Font Stack - Multi-script support */
  --font-primary: 'Noto Sans', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  
  /* Indic Script Fonts */
  --font-devanagari: 'Noto Sans Devanagari', 'Hind', 'Mangal', sans-serif;
  --font-bengali: 'Noto Sans Bengali', 'Hind Siliguri', 'SolaimanLipi', sans-serif;
  --font-tamil: 'Noto Sans Tamil', 'Catamaran', 'Latha', sans-serif;
  --font-telugu: 'Noto Sans Telugu', 'Mallanna', 'Gautami', sans-serif;
  --font-gujarati: 'Noto Sans Gujarati', 'Hind Vadodara', 'Shruti', sans-serif;
  
  /* Monospace for code/data */
  --font-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
}
```

### Type Scale
```css
:root {
  /* Font Sizes - Responsive scale */
  --text-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  --text-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
  --text-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
  --text-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
  --text-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
  --text-2xl: clamp(1.5rem, 1.3rem + 1vw, 1.875rem);
  --text-3xl: clamp(1.875rem, 1.6rem + 1.375vw, 2.25rem);
  --text-4xl: clamp(2.25rem, 1.9rem + 1.75vw, 3rem);

  /* Line Heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;

  /* Font Weights */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
}
```

## Spacing System

### Consistent Spacing Scale
```css
:root {
  /* Base unit: 4px */
  --space-0: 0;
  --space-1: 0.25rem;  /* 4px */
  --space-2: 0.5rem;   /* 8px */
  --space-3: 0.75rem;  /* 12px */
  --space-4: 1rem;     /* 16px */
  --space-5: 1.25rem;  /* 20px */
  --space-6: 1.5rem;   /* 24px */
  --space-8: 2rem;     /* 32px */
  --space-10: 2.5rem;  /* 40px */
  --space-12: 3rem;    /* 48px */
  --space-16: 4rem;    /* 64px */
  --space-20: 5rem;    /* 80px */
  --space-24: 6rem;    /* 96px */
}
```

## Component Library

### Button System
```css
/* Base Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-md);
  font-family: var(--font-primary);
  font-weight: var(--font-medium);
  font-size: var(--text-base);
  line-height: var(--leading-tight);
  text-decoration: none;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  min-height: 44px; /* Touch-friendly */
  position: relative;
  overflow: hidden;
}

/* Button Variants */
.btn--primary {
  background-color: var(--primary-500);
  color: white;
  border-color: var(--primary-500);
}

.btn--primary:hover {
  background-color: var(--primary-700);
  border-color: var(--primary-700);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(46, 91, 168, 0.3);
}

.btn--secondary {
  background-color: var(--secondary-500);
  color: white;
  border-color: var(--secondary-500);
}

.btn--outline {
  background-color: transparent;
  color: var(--primary-500);
  border-color: var(--primary-500);
}

.btn--ghost {
  background-color: transparent;
  color: var(--primary-500);
  border-color: transparent;
}

/* Button Sizes */
.btn--sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
  min-height: 36px;
}

.btn--lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-lg);
  min-height: 52px;
}

/* Loading State */
.btn--loading {
  color: transparent;
}

.btn--loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
```

### Card System
```css
.card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.2s ease-in-out;
}

.card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.card__header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--neutral-200);
}

.card__body {
  padding: var(--space-6);
}

.card__footer {
  padding: var(--space-6);
  border-top: 1px solid var(--neutral-200);
  background-color: var(--neutral-50);
}

/* Card Variants */
.card--elevated {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
}

.card--interactive {
  cursor: pointer;
}

.card--interactive:hover {
  box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
  transform: translateY(-2px);
}
```

## Layout System

### Grid System
```css
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.grid {
  display: grid;
  gap: var(--space-6);
}

.grid--cols-1 { grid-template-columns: 1fr; }
.grid--cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid--cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid--cols-4 { grid-template-columns: repeat(4, 1fr); }

/* Responsive Grid */
@media (max-width: 768px) {
  .grid--cols-2,
  .grid--cols-3,
  .grid--cols-4 {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .grid--cols-3,
  .grid--cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}
```

### Flexbox Utilities
```css
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }
```

## Animation System

### Micro-interactions
```css
:root {
  /* Timing Functions */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Duration */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
}

/* Hover Effects */
.hover-lift {
  transition: transform var(--duration-normal) var(--ease-out);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* Focus States */
.focus-ring {
  outline: 2px solid transparent;
  outline-offset: 2px;
  transition: outline-color var(--duration-fast) var(--ease-out);
}

.focus-ring:focus {
  outline-color: var(--primary-500);
}

/* Loading Animations */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in {
  animation: fadeIn var(--duration-normal) var(--ease-out);
}
```

This design system provides the foundation for creating a cohesive, accessible, and culturally appropriate user interface for VidyaMitra. The next sections will cover specific component implementations and user flow optimizations.

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  Button,
  IconButton,
  Collapse,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Paper,
  Fade,
  Zoom,
  useTheme,
  alpha,
} from '@mui/material';
import {
  TrendingUp as StrengthIcon,
  Warning as WeaknessIcon,
  Lightbulb as OpportunityIcon,
  Error as ThreatIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Download as DownloadIcon,
  Share as ShareIcon,
  Print as PrintIcon,
  Refresh as RefreshIcon,
  CheckCircle as CheckIcon,
  School as SchoolIcon,
  Psychology as PsychologyIcon,
  EmojiEvents as AchievementIcon,
  Group as SocialIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

// SWOT Quadrant Component
const SWOTQuadrant = ({ 
  title, 
  items, 
  color, 
  icon: Icon, 
  expanded, 
  onToggle,
  delay = 0 
}) => {
  const theme = useTheme();
  
  const getCategoryIcon = (category) => {
    switch (category.toLowerCase()) {
      case 'academic': return <SchoolIcon fontSize="small" />;
      case 'behavioral': return <PsychologyIcon fontSize="small" />;
      case 'extracurricular': return <AchievementIcon fontSize="small" />;
      case 'social': return <SocialIcon fontSize="small" />;
      default: return <CheckIcon fontSize="small" />;
    }
  };

  return (
    <Zoom in timeout={300 + delay}>
      <Card
        sx={{
          height: '100%',
          border: `2px solid ${theme.palette[color].main}`,
          borderRadius: 3,
          overflow: 'hidden',
          transition: 'all 0.3s ease-in-out',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: theme.shadows[8],
          },
        }}
      >
        {/* Header */}
        <Box
          sx={{
            bgcolor: theme.palette[color].main,
            color: theme.palette[color].contrastText,
            p: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Icon />
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              {title}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Chip
              label={items.length}
              size="small"
              sx={{
                bgcolor: alpha(theme.palette[color].contrastText, 0.2),
                color: theme.palette[color].contrastText,
                fontWeight: 600,
              }}
            />
            <IconButton
              size="small"
              onClick={onToggle}
              sx={{ color: theme.palette[color].contrastText }}
            >
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>
        </Box>

        {/* Content */}
        <CardContent sx={{ p: 0 }}>
          <Collapse in={expanded} timeout={300}>
            <List sx={{ py: 0 }}>
              {items.map((item, index) => (
                <Fade in timeout={200 + index * 50} key={index}>
                  <Box>
                    <ListItem sx={{ py: 2, px: 3 }}>
                      <ListItemIcon sx={{ minWidth: 36 }}>
                        {getCategoryIcon(item.category)}
                      </ListItemIcon>
                      <ListItemText
                        primary={item.description}
                        secondary={
                          <Box sx={{ mt: 1 }}>
                            <Chip
                              label={item.category}
                              size="small"
                              variant="outlined"
                              sx={{ mr: 1, fontSize: '0.75rem' }}
                            />
                            {item.evidence && item.evidence.length > 0 && (
                              <Typography variant="caption" color="text.secondary">
                                Evidence: {item.evidence.join(', ')}
                              </Typography>
                            )}
                          </Box>
                        }
                        primaryTypographyProps={{
                          variant: 'body2',
                          fontWeight: 500,
                          sx: { lineHeight: 1.4 }
                        }}
                      />
                    </ListItem>
                    {index < items.length - 1 && <Divider />}
                  </Box>
                </Fade>
              ))}
            </List>
          </Collapse>
          
          {/* Collapsed Preview */}
          <Collapse in={!expanded} timeout={300}>
            <Box sx={{ p: 3 }}>
              {items.slice(0, 2).map((item, index) => (
                <Typography
                  key={index}
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    mb: 1,
                    display: '-webkit-box',
                    WebkitLineClamp: 1,
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden',
                  }}
                >
                  • {item.description}
                </Typography>
              ))}
              {items.length > 2 && (
                <Typography variant="caption" color="text.secondary">
                  +{items.length - 2} more items
                </Typography>
              )}
            </Box>
          </Collapse>
        </CardContent>
      </Card>
    </Zoom>
  );
};

// Recommendations Section
const RecommendationsSection = ({ recommendations }) => {
  const theme = useTheme();
  const [expandedRec, setExpandedRec] = useState({});

  const toggleRecommendation = (index) => {
    setExpandedRec(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  const getPriorityColor = (priority) => {
    switch (priority.toLowerCase()) {
      case 'high': return theme.palette.error.main;
      case 'medium': return theme.palette.warning.main;
      case 'low': return theme.palette.success.main;
      default: return theme.palette.text.secondary;
    }
  };

  const getTargetAudienceColor = (audience) => {
    switch (audience.toLowerCase()) {
      case 'student': return theme.palette.primary.main;
      case 'parent': return theme.palette.secondary.main;
      case 'teacher': return theme.palette.success.main;
      default: return theme.palette.text.secondary;
    }
  };

  return (
    <Card sx={{ mt: 3, borderRadius: 3 }}>
      <CardContent sx={{ p: 3 }}>
        <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
          Recommendations & Action Items
        </Typography>
        
        <Grid container spacing={2}>
          {recommendations.map((rec, index) => (
            <Grid item xs={12} key={index}>
              <Fade in timeout={300 + index * 100}>
                <Paper
                  sx={{
                    p: 3,
                    border: `1px solid ${alpha(getPriorityColor(rec.priority), 0.3)}`,
                    borderRadius: 2,
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': {
                      boxShadow: theme.shadows[4],
                    },
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 2 }}>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="body1" sx={{ fontWeight: 500, mb: 1 }}>
                        {rec.description}
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                        <Chip
                          label={rec.priority}
                          size="small"
                          sx={{
                            bgcolor: alpha(getPriorityColor(rec.priority), 0.1),
                            color: getPriorityColor(rec.priority),
                            fontWeight: 600,
                          }}
                        />
                        <Chip
                          label={rec.target_audience}
                          size="small"
                          variant="outlined"
                          sx={{
                            borderColor: getTargetAudienceColor(rec.target_audience),
                            color: getTargetAudienceColor(rec.target_audience),
                          }}
                        />
                        <Chip
                          label={rec.category}
                          size="small"
                          variant="outlined"
                        />
                      </Box>
                    </Box>
                    {rec.action_items && rec.action_items.length > 0 && (
                      <IconButton
                        size="small"
                        onClick={() => toggleRecommendation(index)}
                      >
                        {expandedRec[index] ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                      </IconButton>
                    )}
                  </Box>

                  <Collapse in={expandedRec[index]} timeout={300}>
                    {rec.action_items && rec.action_items.length > 0 && (
                      <Box sx={{ mt: 2, pl: 2, borderLeft: `3px solid ${getPriorityColor(rec.priority)}` }}>
                        <Typography variant="body2" sx={{ fontWeight: 500, mb: 1 }}>
                          Action Items:
                        </Typography>
                        <List dense>
                          {rec.action_items.map((action, actionIndex) => (
                            <ListItem key={actionIndex} sx={{ py: 0.5, px: 0 }}>
                              <ListItemIcon sx={{ minWidth: 24 }}>
                                <CheckIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                              </ListItemIcon>
                              <ListItemText
                                primary={action}
                                primaryTypographyProps={{
                                  variant: 'body2',
                                  color: 'text.secondary'
                                }}
                              />
                            </ListItem>
                          ))}
                        </List>
                      </Box>
                    )}
                  </Collapse>
                </Paper>
              </Fade>
            </Grid>
          ))}
        </Grid>
      </CardContent>
    </Card>
  );
};

// Main SWOT Analysis Visualization Component
const SWOTAnalysisVisualization = ({ studentId, analysisData, loading = false }) => {
  const { t } = useTranslation(['common']);
  const theme = useTheme();
  
  const [expandedQuadrants, setExpandedQuadrants] = useState({
    strengths: true,
    weaknesses: true,
    opportunities: false,
    threats: false,
  });

  const toggleQuadrant = (quadrant) => {
    setExpandedQuadrants(prev => ({
      ...prev,
      [quadrant]: !prev[quadrant]
    }));
  };

  const handleExport = (format) => {
    console.log(`Exporting SWOT analysis as ${format}`);
  };

  const handleRefresh = () => {
    console.log('Refreshing SWOT analysis');
  };

  // Mock data if no analysis data provided
  const mockData = {
    swot: {
      strengths: [
        {
          description: "Excellent performance in Mathematics with consistent A+ grades",
          category: "Academic",
          evidence: ["95% average in Math tests", "Top 3 in class"]
        },
        {
          description: "Strong leadership skills demonstrated in group activities",
          category: "Behavioral",
          evidence: ["Class monitor", "Led science project team"]
        }
      ],
      weaknesses: [
        {
          description: "Needs improvement in English writing skills",
          category: "Academic",
          evidence: ["C grade in essays", "Grammar mistakes"]
        },
        {
          description: "Occasional tardiness affecting attendance record",
          category: "Behavioral",
          evidence: ["5 late arrivals this month"]
        }
      ],
      opportunities: [
        {
          description: "Math Olympiad participation could enhance problem-solving skills",
          category: "Extracurricular",
          evidence: ["Strong math foundation", "Interest in competitions"]
        }
      ],
      threats: [
        {
          description: "Increasing homework load may impact performance",
          category: "Academic",
          evidence: ["Recent decline in assignment quality"]
        }
      ]
    },
    recommendations: [
      {
        description: "Enroll in advanced English writing workshop",
        target_audience: "Parent",
        category: "Academic Improvement",
        priority: "High",
        action_items: [
          "Research local writing workshops",
          "Schedule consultation with English teacher",
          "Set up daily reading routine"
        ]
      },
      {
        description: "Apply for Math Olympiad training program",
        target_audience: "Student",
        category: "Skill Development",
        priority: "Medium",
        action_items: [
          "Complete application form",
          "Prepare for entrance test",
          "Join math study group"
        ]
      }
    ]
  };

  const data = analysisData || mockData;

  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box>
            <Typography variant="h4" component="h1" sx={{ fontWeight: 700, mb: 1 }}>
              SWOT Analysis
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Comprehensive analysis of student strengths, weaknesses, opportunities, and threats
            </Typography>
          </Box>
          
          {/* Action Buttons */}
          <Box sx={{ display: 'flex', gap: 1 }}>
            <IconButton onClick={handleRefresh} disabled={loading}>
              <RefreshIcon />
            </IconButton>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={() => handleExport('pdf')}
              sx={{ display: { xs: 'none', sm: 'flex' } }}
            >
              Export
            </Button>
            <Button
              variant="outlined"
              startIcon={<ShareIcon />}
              onClick={() => handleExport('share')}
              sx={{ display: { xs: 'none', sm: 'flex' } }}
            >
              Share
            </Button>
          </Box>
        </Box>
      </Box>

      {/* SWOT Grid */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <SWOTQuadrant
            title="Strengths"
            items={data.swot.strengths}
            color="success"
            icon={StrengthIcon}
            expanded={expandedQuadrants.strengths}
            onToggle={() => toggleQuadrant('strengths')}
            delay={0}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <SWOTQuadrant
            title="Weaknesses"
            items={data.swot.weaknesses}
            color="warning"
            icon={WeaknessIcon}
            expanded={expandedQuadrants.weaknesses}
            onToggle={() => toggleQuadrant('weaknesses')}
            delay={100}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <SWOTQuadrant
            title="Opportunities"
            items={data.swot.opportunities}
            color="info"
            icon={OpportunityIcon}
            expanded={expandedQuadrants.opportunities}
            onToggle={() => toggleQuadrant('opportunities')}
            delay={200}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <SWOTQuadrant
            title="Threats"
            items={data.swot.threats}
            color="error"
            icon={ThreatIcon}
            expanded={expandedQuadrants.threats}
            onToggle={() => toggleQuadrant('threats')}
            delay={300}
          />
        </Grid>
      </Grid>

      {/* Recommendations */}
      <RecommendationsSection recommendations={data.recommendations} />
    </Box>
  );
};

export default SWOTAnalysisVisualization;

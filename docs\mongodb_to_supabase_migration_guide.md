# MongoDB to Supabase Migration Guide
## Student SWOT Analysis Platform

This document outlines the process for migrating the Student SWOT Analysis Platform from MongoDB to Supabase, with specific considerations for the Indian education data model.

## Table of Contents

1. [Introduction](#introduction)
2. [Migration Overview](#migration-overview)
3. [Prerequisite Steps](#prerequisite-steps)
4. [Data Migration Process](#data-migration-process)
5. [Application Code Changes](#application-code-changes)
6. [Authentication Migration](#authentication-migration)
7. [Testing Strategy](#testing-strategy)
8. [Rollback Procedure](#rollback-procedure)
9. [Post-Migration Steps](#post-migration-steps)
10. [Troubleshooting](#troubleshooting)

## Introduction

### Why Migrate from MongoDB to Supabase?

- **SQL Benefits**: Moving from NoSQL to PostgreSQL provides better data integrity, relationships, and complex querying capabilities
- **Integrated Services**: Supabase offers auth, storage, and realtime functionality in one platform
- **Simplified Management**: Reduced infrastructure management overhead
- **Cost Efficiency**: Potentially lower costs with Supabase's pricing model
- **Row-Level Security**: Enhanced data security through built-in RLS policies

### Key Differences between MongoDB and Supabase

| Feature | MongoDB | Supabase (PostgreSQL) |
|---------|---------|------------------------|
| **Data Model** | Document-based (BSON) | Relational (SQL) |
| **Schema** | Flexible schema | Strict schema |
| **Queries** | MongoDB Query API | SQL + Supabase JS Client |
| **Relationships** | Referenced or embedded | Foreign key constraints |
| **Transactions** | Multi-document transactions | ACID transactions |
| **Indexing** | Various index types | Multiple index types + text search |
| **Authentication** | Custom solution | Built-in Auth with JWT |
| **Realtime** | Change streams | Built-in Realtime with subscriptions |

## Migration Overview

### Migration Phases

1. **Planning & Preparation** (1-2 weeks)
   - Database schema design
   - Environment setup
   - Team training

2. **Development & Testing** (2-3 weeks)
   - Data migration scripts
   - Application code updates
   - Test environment setup

3. **Validation & QA** (1-2 weeks)
   - Comprehensive testing
   - Performance benchmarks
   - Security validation

4. **Deployment** (1 week)
   - Final data migration
   - Application deployment
   - Monitoring setup

5. **Support & Optimization** (Ongoing)
   - Performance tuning
   - Issue resolution
   - Feature enhancements

### Timeline Considerations

- Total estimated time: 5-8 weeks
- Suggested migration during academic holidays to minimize disruption
- Consider a phased rollout by school or region

## Prerequisite Steps

### Set Up Supabase Project

1. Create a Supabase project at [https://app.supabase.io](https://app.supabase.io)
2. Note your project URL and API keys
3. Configure project settings:
   - Enable Row Level Security (RLS)
   - Configure email templates for auth
   - Set up storage buckets

### Install Required Tools

```bash
# Install Supabase CLI
npm install -g supabase

# Install data migration tools
npm install pg
npm install mongodb
npm install dotenv
```

### Configure Environment Variables

Create a `.env` file:

```
# MongoDB Connection
MONGODB_URI=************************************:port/database

# Supabase Connection
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-key
```

## Data Migration Process

### 1. Create PostgreSQL Tables in Supabase

Run the SQL script provided in `supabase_schema.sql` to create all necessary tables, indexes, and security policies.

### 2. Set Up Data Migration Scripts

Create scripts for each data collection migration.

Example migration script framework:

```javascript
require('dotenv').config();
const { MongoClient } = require('mongodb');
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

// Connection Setup
const mongoUri = process.env.MONGODB_URI;
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

// Initialize clients
const mongoClient = new MongoClient(mongoUri);
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function migrateCollection(collectionName, transformFunction, targetTable) {
  try {
    await mongoClient.connect();
    console.log(`Connected to MongoDB for migrating ${collectionName}`);
    
    const database = mongoClient.db();
    const collection = database.collection(collectionName);
    
    // Get documents in batches
    const batchSize = 100;
    let skip = 0;
    let totalMigrated = 0;
    let hasMore = true;
    
    while (hasMore) {
      const documents = await collection.find({})
        .skip(skip)
        .limit(batchSize)
        .toArray();
      
      if (documents.length === 0) {
        hasMore = false;
        break;
      }
      
      // Transform MongoDB documents to PostgreSQL format
      const transformedData = documents.map(transformFunction);
      
      // Insert into Supabase table
      const { error } = await supabase.from(targetTable).insert(transformedData);
      
      if (error) {
        console.error(`Error inserting batch starting at ${skip}:`, error);
        // Write failed records to file for later retry
        fs.writeFileSync(
          `failed_${collectionName}_${skip}.json`,
          JSON.stringify(transformedData, null, 2)
        );
      } else {
        totalMigrated += documents.length;
        console.log(`Migrated ${documents.length} documents from ${skip}. Total: ${totalMigrated}`);
      }
      
      skip += batchSize;
    }
    
    console.log(`Migration completed for ${collectionName}. Total migrated: ${totalMigrated}`);
  } catch (error) {
    console.error(`Error migrating ${collectionName}:`, error);
  }
}

// Example transform function for students
function transformStudent(document) {
  return {
    id: document._id.toString(), // Convert MongoDB ObjectId to string
    school_id: document.schoolId,
    admission_number: document.admissionNumber,
    roll_number: document.rollNumber,
    first_name: document.firstName,
    last_name: document.lastName,
    gender: document.gender,
    date_of_birth: document.dateOfBirth,
    class_id: document.classId,
    contact_phone: document.contactPhone,
    contact_email: document.contactEmail,
    address: document.address,
    city: document.city,
    state: document.state,
    pincode: document.pincode,
    caste_category: document.casteCategory,
    religion: document.religion,
    nationality: document.nationality || 'Indian',
    aadhaar_number: document.aadhaarNumber,
    created_at: document.createdAt || new Date().toISOString(),
    updated_at: document.updatedAt || new Date().toISOString()
  };
}

// Run migrations in sequence to respect foreign key constraints
async function migrateAll() {
  try {
    // Migrate in order of dependencies
    await migrateCollection('schools', transformSchool, 'schools');
    await migrateCollection('academicYears', transformAcademicYear, 'academic_years');
    await migrateCollection('academicTerms', transformAcademicTerm, 'academic_terms');
    await migrateCollection('classes', transformClass, 'classes');
    await migrateCollection('subjects', transformSubject, 'subjects');
    await migrateCollection('classSubjects', transformClassSubject, 'class_subjects');
    await migrateCollection('students', transformStudent, 'students');
    // Continue with other collections...
    
    console.log('All migrations completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    await mongoClient.close();
  }
}

migrateAll();
```

### 3. Data Transformation Considerations

**MongoDB Documents to PostgreSQL Tables:**

- Convert MongoDB `_id` fields to UUID strings
- Flatten nested objects into separate tables
- Convert MongoDB arrays to PostgreSQL arrays or junction tables
- Handle date conversions properly
- Create relationships with foreign keys

**Indian Education Model Specific Transformations:**

- Map board-specific grading systems
- Convert quarter references to Indian term structure
- Map CCE assessment data into appropriate tables
- Transform extracurricular activities to include Indian-specific categories

### 4. Execute Migration in Stages

Run migrations in this recommended order:

1. Reference/lookup data (schools, academic years, subjects)
2. Structural data (classes, terms)
3. Core data (students, teachers, guardians)
4. Transactional data (attendance, academic performance, behavior)
5. Analysis data (SWOT analyses, recommendations)

### 5. Validate Migration Results

For each migrated table:

- Verify record counts match source collections
- Validate data integrity and relationships
- Check for any data conversion issues

## Application Code Changes

### Client Configuration Updates

**Replace MongoDB client with Supabase client:**

```javascript
// Old MongoDB setup
import { MongoClient } from 'mongodb';
const client = new MongoClient(process.env.MONGODB_URI);
await client.connect();
const db = client.db('swot-platform');

// New Supabase setup
import { createClient } from '@supabase/supabase-js';
const supabase = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_ANON_KEY
);
```

### API Service Updates

Convert all service modules to use Supabase queries:

**MongoDB Query:**
```javascript
const students = await db.collection('students')
  .find({ classId: ObjectId(classId) })
  .sort({ rollNumber: 1 })
  .toArray();
```

**Supabase Query:**
```javascript
const { data: students, error } = await supabase
  .from('students')
  .select('*')
  .eq('class_id', classId)
  .order('roll_number', { ascending: true });
```

### Replace MongoDB Specific Operations

| MongoDB Operation | Supabase Equivalent |
|-------------------|---------------------|
| `$set` | `.update()` |
| `$push` | Use PostgreSQL array functions |
| `$in` | `.in()` |
| `$and` | Chain `.eq()` methods |
| `$or` | `.or()` |
| Aggregation | Use PostgreSQL GROUP BY or create views |

### Handle Transaction Logic

**MongoDB Transactions:**
```javascript
const session = client.startSession();
try {
  await session.withTransaction(async () => {
    await collection1.insertOne(doc1, { session });
    await collection2.updateOne(filter, update, { session });
  });
} finally {
  await session.endSession();
}
```

**Supabase Note:** Supabase doesn't directly expose transaction API in the client, so you'll need to:

1. Create PostgreSQL functions for operations requiring transactions
2. Call these functions from your client code

Example PostgreSQL function:
```sql
CREATE OR REPLACE FUNCTION create_swot_analysis(
  p_student_id UUID,
  p_academic_term_id UUID,
  p_generated_by UUID
) 
RETURNS UUID AS $$
DECLARE
  v_swot_id UUID;
BEGIN
  -- Insert SWOT analysis and get ID
  INSERT INTO swot_analysis (student_id, academic_term_id, generated_by)
  VALUES (p_student_id, p_academic_term_id, p_generated_by)
  RETURNING id INTO v_swot_id;
  
  -- Add default items
  INSERT INTO swot_items (swot_analysis_id, category, description, priority)
  VALUES
    (v_swot_id, 'Strength', 'Initial strength item', 'Medium'),
    (v_swot_id, 'Weakness', 'Initial weakness item', 'Medium'),
    (v_swot_id, 'Opportunity', 'Initial opportunity item', 'Medium'),
    (v_swot_id, 'Threat', 'Initial threat item', 'Medium');
    
  RETURN v_swot_id;
END;
$$ LANGUAGE plpgsql;
```

Then call from client:
```javascript
const { data, error } = await supabase.rpc('create_swot_analysis', {
  p_student_id: studentId,
  p_academic_term_id: termId,
  p_generated_by: userId
});
```

## Authentication Migration

### Migrating User Accounts

1. **Export Users from MongoDB**

```javascript
const users = await db.collection('users').find({}).toArray();
const exportedUsers = users.map(user => ({
  email: user.email,
  password_hash: user.passwordHash, // Note: Likely incompatible with Supabase
  metadata: {
    firstName: user.firstName,
    lastName: user.lastName,
    role: user.role
  }
}));
fs.writeFileSync('users_export.json', JSON.stringify(exportedUsers));
```

2. **Create Accounts in Supabase**

Users will need to be recreated in Supabase Auth, with:
- Password reset emails to set new passwords
- Or use Admin API to create users with new passwords

3. **Create User Profiles Table**

```sql
CREATE TABLE user_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  school_id UUID REFERENCES schools(id) ON DELETE SET NULL,
  first_name TEXT,
  last_name TEXT,
  role TEXT NOT NULL,
  email TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(user_id)
);
```

4. **Set Up RLS Policies**

```sql
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Allow users to read their own profile
CREATE POLICY "Users can view own profile" 
ON user_profiles FOR SELECT 
USING (auth.uid() = user_id);

-- Allow users to update their own profile
CREATE POLICY "Users can update own profile" 
ON user_profiles FOR UPDATE
USING (auth.uid() = user_id);

-- Admin policies
CREATE POLICY "Admins can view all profiles" 
ON user_profiles FOR SELECT 
USING (
  auth.uid() IN (
    SELECT user_id FROM user_profiles WHERE role = 'admin'
  )
);
```

### Update Authentication in Frontend

Replace custom auth methods with Supabase auth:

```javascript
// Old MongoDB-based auth
const login = async (email, password) => {
  const response = await fetch('/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email, password })
  });
  
  const data = await response.json();
  if (data.token) {
    localStorage.setItem('token', data.token);
    return { success: true, user: data.user };
  }
  
  return { success: false, message: data.message };
};

// New Supabase auth
const login = async (email, password) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  });
  
  if (error) {
    return { success: false, message: error.message };
  }
  
  return { success: true, user: data.user };
};
```

## Testing Strategy

### Test Environment Setup

1. Create a separate Supabase project for testing
2. Migrate a subset of data for testing purposes
3. Configure the application to use the test environment

### Testing Approach

1. **Functional Testing**
   - Verify all CRUD operations work correctly
   - Test authentication flows
   - Validate data filtering and pagination

2. **Data Integrity Testing**
   - Validate relationships between tables
   - Ensure constraints are enforced
   - Test data type conversions

3. **Performance Testing**
   - Compare query performance between MongoDB and Supabase
   - Test concurrent user scenarios
   - Measure API response times

4. **Security Testing**
   - Verify Row Level Security policies
   - Test authentication and authorization
   - Validate API endpoint security

5. **Integration Testing**
   - Test system-to-system interfaces
   - Verify third-party service integrations
   - Validate workflow processes

### Test Scripts

Create a complete test suite with integration tests. Example:

```javascript
// Student CRUD operations test
describe('Student Service Tests', () => {
  let testStudentId;
  
  test('Create Student', async () => {
    const studentData = {
      school_id: 'test-school-id',
      first_name: 'Test',
      last_name: 'Student',
      admission_number: 'TST12345',
      roll_number: '101',
      class_id: 'test-class-id'
    };
    
    const response = await studentService.createStudent(studentData);
    testStudentId = response.id;
    
    expect(response).toBeDefined();
    expect(response.first_name).toBe('Test');
    expect(response.last_name).toBe('Student');
  });
  
  test('Get Student By ID', async () => {
    const student = await studentService.getStudentById(testStudentId);
    
    expect(student).toBeDefined();
    expect(student.id).toBe(testStudentId);
    expect(student.admission_number).toBe('TST12345');
  });
  
  // Additional tests...
});
```

## Rollback Procedure

### Rollback Conditions

Consider rolling back if:
- Critical functionality is broken
- Data integrity issues are discovered
- Performance is significantly degraded
- Security vulnerabilities are identified

### Rollback Steps

1. **Application Rollback**
   - Switch application back to MongoDB connection
   - Deploy the previous version of the application
   - Update environment variables

2. **Data Verification**
   - Verify MongoDB data integrity
   - Check for any data loss from the migration process

3. **User Communication**
   - Inform users of the rollback
   - Provide guidance on any temporary process changes
   - Set expectations for next migration attempt

### Rollback Checklist

- [ ] Backup Supabase data
- [ ] Verify MongoDB availability
- [ ] Deploy previous application version
- [ ] Update DNS/routing configurations
- [ ] Test critical functionality
- [ ] Send user notifications
- [ ] Update monitoring systems
- [ ] Document rollback reason

## Post-Migration Steps

### Optimization

1. **Performance Tuning**
   - Add additional indexes based on query patterns
   - Optimize complex queries
   - Consider materialized views for frequent calculations

2. **Security Hardening**
   - Review RLS policies
   - Audit authentication mechanisms
   - Implement additional monitoring

### Monitoring and Maintenance

1. Set up Supabase monitoring
2. Create performance dashboards
3. Schedule regular database maintenance

### Documentation Updates

1. Update system architecture documentation
2. Create Supabase-specific guides
3. Update API documentation

## Troubleshooting

### Common Migration Issues

1. **Data Type Conversion Errors**
   - **Problem**: MongoDB and PostgreSQL handle data types differently
   - **Solution**: Add explicit type conversions in migration scripts

2. **Foreign Key Constraint Violations**
   - **Problem**: Referenced records don't exist when inserting data
   - **Solution**: Migrate data in order of dependencies

3. **RLS Policy Issues**
   - **Problem**: Users can't access data they should have permissions for
   - **Solution**: Review and test RLS policies thoroughly

4. **Performance Regression**
   - **Problem**: Queries are slower in PostgreSQL than MongoDB
   - **Solution**: Add appropriate indexes and optimize queries

### Support Resources

- [Supabase Documentation](https://supabase.io/docs)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Supabase GitHub Issues](https://github.com/supabase/supabase/issues)
- [Supabase Discord Community](https://discord.supabase.com)

## Conclusion

Migrating from MongoDB to Supabase offers significant benefits for the Student SWOT Analysis Platform, including improved data integrity, simplified infrastructure, and enhanced security. While the migration process requires careful planning and execution, the long-term advantages make it a worthwhile investment.

By following this guide, you can achieve a successful migration with minimal disruption to your users while positioning the platform for future growth and enhancements.
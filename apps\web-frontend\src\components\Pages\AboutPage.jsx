import React from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Avatar,
  Chip,
  Button,
  Paper,
  Fade,
  useTheme,
} from '@mui/material';
import {
  School as SchoolIcon,
  Psychology as PsychologyIcon,
  Groups as GroupsIcon,
  TrendingUp as TrendingUpIcon,
  ArrowBack as ArrowBackIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const AboutPage = () => {
  const { t } = useTranslation('common');
  const navigate = useNavigate();
  const theme = useTheme();

  const values = [
    {
      icon: SchoolIcon,
      title: 'Educational Excellence',
      description: 'Committed to enhancing the quality of education through data-driven insights and personalized learning approaches.',
      color: theme.palette.primary.main,
    },
    {
      icon: PsychologyIcon,
      title: 'AI-Powered Innovation',
      description: 'Leveraging cutting-edge artificial intelligence to provide meaningful and actionable student analysis.',
      color: theme.palette.secondary.main,
    },
    {
      icon: GroupsIcon,
      title: 'Collaborative Approach',
      description: 'Bringing together teachers, parents, and administrators in a unified platform for student success.',
      color: theme.palette.success.main,
    },
    {
      icon: TrendingUpIcon,
      title: 'Continuous Improvement',
      description: 'Constantly evolving our platform based on user feedback and educational best practices.',
      color: theme.palette.warning.main,
    },
  ];

  const stats = [
    { number: '1000+', label: 'Students Analyzed' },
    { number: '50+', label: 'Partner Schools' },
    { number: '95%', label: 'User Satisfaction' },
    { number: '3', label: 'Educational Boards' },
  ];

  return (
    <Box>
      {/* Header */}
      <Box sx={{ bgcolor: 'primary.main', color: 'white', py: 8 }}>
        <Container maxWidth="lg">
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/')}
            sx={{ color: 'white', mb: 4 }}
          >
            Back to Home
          </Button>
          <Typography variant="h2" sx={{ fontWeight: 700, mb: 3 }}>
            About VidyaMitra
          </Typography>
          <Typography variant="h5" sx={{ opacity: 0.9, maxWidth: 800 }}>
            Revolutionizing Indian education through intelligent student analysis and 
            comprehensive SWOT insights tailored for CBSE, ICSE, and State boards.
          </Typography>
        </Container>
      </Box>

      {/* Mission & Vision */}
      <Box sx={{ py: 8, bgcolor: 'background.default' }}>
        <Container maxWidth="lg">
          <Grid container spacing={6}>
            <Grid item xs={12} md={6}>
              <Fade in timeout={800}>
                <Paper elevation={2} sx={{ p: 4, height: '100%' }}>
                  <Typography variant="h4" sx={{ fontWeight: 600, mb: 3, color: 'primary.main' }}>
                    Our Mission
                  </Typography>
                  <Typography variant="body1" sx={{ lineHeight: 1.8, mb: 3 }}>
                    To empower educators, parents, and administrators with comprehensive SWOT analysis 
                    tools that provide actionable insights for student development across all Indian 
                    educational boards.
                  </Typography>
                  <Typography variant="body1" sx={{ lineHeight: 1.8 }}>
                    We believe every student has unique strengths and potential. Our platform helps 
                    identify these strengths while addressing weaknesses through personalized 
                    recommendations and data-driven strategies.
                  </Typography>
                </Paper>
              </Fade>
            </Grid>
            <Grid item xs={12} md={6}>
              <Fade in timeout={1000}>
                <Paper elevation={2} sx={{ p: 4, height: '100%' }}>
                  <Typography variant="h4" sx={{ fontWeight: 600, mb: 3, color: 'secondary.main' }}>
                    Our Vision
                  </Typography>
                  <Typography variant="body1" sx={{ lineHeight: 1.8, mb: 3 }}>
                    To become the leading platform for student analysis in India, helping create 
                    a generation of well-rounded individuals who are prepared for the challenges 
                    of the 21st century.
                  </Typography>
                  <Typography variant="body1" sx={{ lineHeight: 1.8 }}>
                    We envision a future where every student receives personalized attention and 
                    guidance, leading to improved academic outcomes and holistic development.
                  </Typography>
                </Paper>
              </Fade>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Values */}
      <Box sx={{ py: 8, bgcolor: 'background.paper' }}>
        <Container maxWidth="lg">
          <Typography variant="h3" sx={{ textAlign: 'center', fontWeight: 700, mb: 6 }}>
            Our Core Values
          </Typography>
          <Grid container spacing={4}>
            {values.map((value, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Fade in timeout={800 + index * 200}>
                  <Card
                    sx={{
                      height: '100%',
                      textAlign: 'center',
                      p: 3,
                      transition: 'all 0.3s ease-in-out',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: theme.shadows[8],
                      },
                    }}
                  >
                    <Avatar
                      sx={{
                        width: 64,
                        height: 64,
                        bgcolor: value.color,
                        mx: 'auto',
                        mb: 2,
                      }}
                    >
                      <value.icon sx={{ fontSize: 32 }} />
                    </Avatar>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                      {value.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {value.description}
                    </Typography>
                  </Card>
                </Fade>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Statistics */}
      <Box sx={{ py: 8, bgcolor: 'primary.main', color: 'white' }}>
        <Container maxWidth="lg">
          <Typography variant="h3" sx={{ textAlign: 'center', fontWeight: 700, mb: 6 }}>
            Our Impact
          </Typography>
          <Grid container spacing={4}>
            {stats.map((stat, index) => (
              <Grid item xs={6} md={3} key={index}>
                <Fade in timeout={800 + index * 200}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h2" sx={{ fontWeight: 800, mb: 1 }}>
                      {stat.number}
                    </Typography>
                    <Typography variant="h6" sx={{ opacity: 0.9 }}>
                      {stat.label}
                    </Typography>
                  </Box>
                </Fade>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Call to Action */}
      <Box sx={{ py: 8, bgcolor: 'background.default', textAlign: 'center' }}>
        <Container maxWidth="md">
          <Typography variant="h3" sx={{ fontWeight: 700, mb: 3 }}>
            Ready to Transform Education?
          </Typography>
          <Typography variant="h6" color="text.secondary" sx={{ mb: 4 }}>
            Join thousands of educators who are already using VidyaMitra to enhance student outcomes.
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              onClick={() => navigate('/login')}
              sx={{ px: 4, py: 1.5 }}
            >
              Get Started Today
            </Button>
            <Button
              variant="outlined"
              size="large"
              onClick={() => navigate('/contact')}
              sx={{ px: 4, py: 1.5 }}
            >
              Contact Us
            </Button>
          </Box>
        </Container>
      </Box>
    </Box>
  );
};

export default AboutPage;

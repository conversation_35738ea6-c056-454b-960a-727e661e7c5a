# Data Model for Student SWOT Analysis Platform

## Entity-Relationship Diagram (Conceptual)

```
+---------------+       +----------------+       +--------------------+
|   Students    |       |   Guardians    |       | AcademicPerformance|
+---------------+       +----------------+       +--------------------+
| student_id PK |<----->| guardian_id PK |       | performance_id PK  |
| name          |       | student_id FK  |       | student_id FK      |
| grade_level   |       | name           |       | quarter            |
| homeroom      |       | relationship   |       | academic_year      |
| academic_year |       | email          |       | overall_gpa        |
+---------------+       | phone          |       +--------------------+
        |               +----------------+                |
        |                                                 |
        v                                                 v
+---------------+       +----------------+       +--------------------+
|   Attendance  |       |  Behavioral    |       |      Subjects      |
+---------------+       |  Incidents     |       +--------------------+
| attendance_id |       +----------------+       | subject_id PK      |
| student_id FK |       | incident_id PK |       | performance_id FK  |
| date          |       | student_id FK  |       | subject_name       |
| status        |       | date           |       | score              |
| tardiness     |       | type           |       | grade              |
| excused       |       | category       |       | teacher_comments   |
| reason        |       | description    |       +--------------------+
+---------------+       | action_taken   |
        |               | reported_by    |
        |               +----------------+
        v                        |
+------------------+             |
| QuarterlyAttend  |             |
+------------------+             v
| summary_id PK    |    +--------------------+
| student_id FK    |    |  ExtracurricularAct|
| quarter          |    +--------------------+
| academic_year    |    | activity_id PK     |
| present_days     |    | student_id FK      |
| absent_days      |    | academic_year      |
| tardy_days       |    | activity_name      |
| attendance_rate  |    | role               |
+------------------+    | hours_per_week     |
                        | advisor            |
                        | attendance_rate    |
                        +--------------------+
```

## Table Definitions

### Students
- **student_id** (PK): Unique identifier for each student
- **name**: Student's full name
- **grade_level**: Current grade level (e.g., 9, 10, 11, 12)
- **homeroom**: Homeroom designation
- **academic_year**: Current academic year (e.g., "2024-2025")

### Guardians
- **guardian_id** (PK): Unique identifier for each guardian
- **student_id** (FK): Reference to the student
- **name**: Guardian's full name
- **relationship**: Relationship to student (e.g., "Father", "Mother", "Guardian")
- **email**: Contact email
- **phone**: Contact phone number

### AcademicPerformance
- **performance_id** (PK): Unique identifier for each performance record
- **student_id** (FK): Reference to the student
- **quarter**: Academic quarter (1, 2, 3, 4)
- **academic_year**: Academic year (e.g., "2024-2025")
- **overall_gpa**: Overall GPA for the quarter

### Subjects
- **subject_id** (PK): Unique identifier for each subject record
- **performance_id** (FK): Reference to the academic performance record
- **subject_name**: Name of the subject
- **score**: Numeric score
- **grade**: Letter grade
- **teacher_comments**: Comments from the teacher

### Attendance
- **attendance_id** (PK): Unique identifier for each attendance record
- **student_id** (FK): Reference to the student
- **date**: Date of attendance record
- **status**: Status (present, absent, etc.)
- **tardiness**: Minutes late (if applicable)
- **excused**: Boolean indicating if absence was excused
- **reason**: Reason for absence (if applicable)

### QuarterlyAttendance
- **summary_id** (PK): Unique identifier for each summary
- **student_id** (FK): Reference to the student
- **quarter**: Academic quarter
- **academic_year**: Academic year
- **present_days**: Number of days present
- **absent_days**: Number of days absent
- **tardy_days**: Number of days tardy
- **attendance_rate**: Percentage of attendance

### BehavioralIncidents
- **incident_id** (PK): Unique identifier for each incident
- **student_id** (FK): Reference to the student
- **date**: Date of incident
- **type**: Type of incident (positive, negative)
- **category**: Category of incident (for negative incidents)
- **description**: Description of the incident
- **action_taken**: Action taken (for negative incidents)
- **reported_by**: Staff member who reported the incident

### ExtracurricularActivities
- **activity_id** (PK): Unique identifier for each activity record
- **student_id** (FK): Reference to the student
- **academic_year**: Academic year
- **activity_name**: Name of the activity
- **role**: Student's role in the activity
- **hours_per_week**: Hours spent per week
- **advisor**: Activity advisor/coach
- **attendance_rate**: Attendance rate for the activity
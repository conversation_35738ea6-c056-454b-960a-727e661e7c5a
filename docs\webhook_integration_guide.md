# SWOT Analysis Platform Webhook Integration Guide

## Introduction

Webhooks provide a powerful mechanism for receiving real-time updates from the SWOT Analysis Platform. Rather than constantly polling the API for changes, your systems can receive immediate notifications when important events occur, such as new grades being recorded, attendance updates, or completed SWOT analyses.

This guide provides comprehensive information about the webhook system, including setup, security, payload formats, and implementation examples.

## Table of Contents

1. [Webhook Overview](#webhook-overview)
2. [Setting Up Webhooks](#setting-up-webhooks)
3. [Webhook Security](#webhook-security)
4. [Event Types](#event-types)
5. [Payload Format](#payload-format)
6. [Handling Webhooks](#handling-webhooks)
7. [Best Practices](#best-practices)
8. [Troubleshooting](#troubleshooting)

## Webhook Overview

A webhook is an HTTP callback: a HTTP POST that occurs when something happens; a simple event-notification via HTTP POST. The SWOT Analysis Platform uses webhooks to notify your systems about events in real-time.

### Key Benefits

1. **Real-time Updates**: Receive notifications immediately when events occur
2. **Reduced API Load**: Eliminate the need for frequent polling 
3. **Event-Driven Architecture**: Build reactive systems that respond to changes
4. **Automation**: Trigger automated workflows based on specific events

## Setting Up Webhooks

### Registration Process

To begin receiving webhook notifications, you must register a webhook endpoint with the SWOT Analysis Platform.

#### API Endpoint

```
POST /api/webhooks/register
```

#### Request Format

```json
{
  "callbackUrl": "https://your-system.example.com/webhooks/swot",
  "events": [
    "student.data.updated",
    "academic.grade.added",
    "swot.analysis.completed"
  ],
  "secret": "your_secure_webhook_secret",
  "description": "Integration with School Information System"
}
```

| Field | Description |
|-------|-------------|
| `callbackUrl` | The URL that will receive webhook POST requests |
| `events` | Array of event types you want to subscribe to (see [Event Types](#event-types)) |
| `secret` | A secure string used to sign webhook payloads for verification |
| `description` | Optional description to help identify this webhook's purpose |

#### Response Format

```json
{
  "success": true,
  "webhookId": "WHK78901",
  "events": [
    "student.data.updated",
    "academic.grade.added",
    "swot.analysis.completed"
  ],
  "status": "active"
}
```

### Managing Webhooks

#### List All Webhooks

```
GET /api/webhooks
```

Response:
```json
{
  "success": true,
  "webhooks": [
    {
      "webhookId": "WHK78901",
      "callbackUrl": "https://your-system.example.com/webhooks/swot",
      "events": [
        "student.data.updated",
        "academic.grade.added"
      ],
      "status": "active",
      "createdAt": "2025-04-10T09:15:23Z",
      "lastTriggered": "2025-05-15T14:22:10Z"
    }
  ]
}
```

#### Update a Webhook

```
PUT /api/webhooks/:webhookId
```

Request:
```json
{
  "events": [
    "student.data.updated",
    "academic.grade.added",
    "attendance.updated"
  ],
  "status": "active"
}
```

Response:
```json
{
  "success": true,
  "webhookId": "WHK78901",
  "events": [
    "student.data.updated",
    "academic.grade.added",
    "attendance.updated"
  ],
  "status": "active"
}
```

#### Delete a Webhook

```
DELETE /api/webhooks/:webhookId
```

Response:
```json
{
  "success": true,
  "message": "Webhook WHK78901 successfully deleted"
}
```

## Webhook Security

Security is critical when implementing webhooks. The SWOT Analysis Platform uses a signature verification mechanism to ensure that webhook requests are authentic.

### Signature Verification

Each webhook request includes a signature header (`X-SWOT-Signature`) that should be verified before processing the payload:

1. The signature is created using HMAC-SHA256
2. The signature uses the secret you provided during webhook registration
3. The payload is the raw, unmodified request body

### Verification Process

Here's how to verify a webhook signature:

```javascript
// Node.js example
const crypto = require('crypto');

function verifyWebhookSignature(requestBody, signatureHeader, secret) {
  // Get the signature from the header
  if (!signatureHeader) {
    return false;
  }
  
  // Create a signature using your secret
  const hmac = crypto.createHmac('sha256', secret);
  const expectedSignature = 'sha256=' + hmac.update(JSON.stringify(requestBody)).digest('hex');
  
  // Compare signatures using a timing-safe comparison
  try {
    return crypto.timingSafeEqual(
      Buffer.from(signatureHeader), 
      Buffer.from(expectedSignature)
    );
  } catch (e) {
    return false;
  }
}

// Usage in an Express.js middleware
function webhookVerificationMiddleware(req, res, next) {
  const signature = req.headers['x-swot-signature'];
  const isValid = verifyWebhookSignature(req.body, signature, WEBHOOK_SECRET);
  
  if (!isValid) {
    return res.status(401).send('Invalid signature');
  }
  
  next();
}
```

```python
# Python example
import hmac
import hashlib
import json

def verify_webhook_signature(request_body, signature_header, secret):
    if not signature_header:
        return False
        
    # Create expected signature
    expected_signature = 'sha256=' + hmac.new(
        key=secret.encode('utf-8'),
        msg=json.dumps(request_body).encode('utf-8'),
        digestmod=hashlib.sha256
    ).hexdigest()
    
    # Compare signatures (constant-time comparison to prevent timing attacks)
    return hmac.compare_digest(signature_header, expected_signature)

# Usage in a Flask route
@app.route('/webhooks/swot', methods=['POST'])
def handle_webhook():
    signature = request.headers.get('X-SWOT-Signature')
    
    if not verify_webhook_signature(request.json, signature, WEBHOOK_SECRET):
        return jsonify({'error': 'Invalid signature'}), 401
    
    # Process the webhook...
    return jsonify({'success': True}), 200
```

### Security Best Practices

1. **Use HTTPS**: Always use HTTPS for your webhook endpoint
2. **Keep Secrets Secure**: Store your webhook secret securely (environment variables, secure vaults)
3. **Validate All Requests**: Always verify the signature before processing
4. **Implement IP Allowlisting**: Consider restricting webhook endpoints to known IP ranges
5. **Rotate Secrets**: Periodically update your webhook secret

## Event Types

The SWOT Analysis Platform supports the following webhook event types:

| Event Type | Description | Example Use Case |
|------------|-------------|------------------|
| `student.data.updated` | Triggered when student profile data is updated | Update student records in SIS |
| `academic.grade.added` | Triggered when a new grade is recorded | Update gradebook, send alerts for low grades |
| `academic.grade.updated` | Triggered when an existing grade is modified | Update gradebook, recalculate GPA |
| `attendance.updated` | Triggered when attendance records are updated | Update attendance system, send absence alerts |
| `behavior.incident.created` | Triggered when a new behavioral incident is recorded | Update behavior tracking system |
| `behavior.incident.updated` | Triggered when a behavioral incident is updated | Update resolution status in tracking system |
| `swot.analysis.completed` | Triggered when a SWOT analysis is generated | Update dashboard, send notification to teachers |
| `swot.analysis.updated` | Triggered when a SWOT analysis is updated | Refresh dashboard displays |
| `report.generated` | Triggered when a report is generated | Notify relevant staff, update report repository |

## Payload Format

Webhook payloads follow a consistent format across all event types:

```json
{
  "eventId": "EVT123456",
  "eventType": "academic.grade.added",
  "timestamp": "2025-05-16T15:32:10Z",
  "resource": {
    "type": "student",
    "id": "STU12345"
  },
  "data": {
    // Event-specific data
  }
}
```

| Field | Description |
|-------|-------------|
| `eventId` | Unique identifier for this event |
| `eventType` | The type of event that occurred |
| `timestamp` | ISO 8601 timestamp when the event occurred |
| `resource` | Information about the primary resource affected |
| `data` | Event-specific payload data |

### Event-Specific Payload Examples

#### student.data.updated

```json
{
  "eventId": "EVT123456",
  "eventType": "student.data.updated",
  "timestamp": "2025-05-16T15:32:10Z",
  "resource": {
    "type": "student",
    "id": "STU12345"
  },
  "data": {
    "fields": ["firstName", "lastName", "grade"],
    "significantChange": false
  }
}
```

#### academic.grade.added

```json
{
  "eventId": "EVT234567",
  "eventType": "academic.grade.added",
  "timestamp": "2025-05-16T15:40:22Z",
  "resource": {
    "type": "student",
    "id": "STU12345"
  },
  "data": {
    "subject": "Mathematics",
    "assignment": "Final Exam",
    "score": 85,
    "maxScore": 100,
    "grade": "B",
    "teacher": "TCH45678",
    "date": "2025-05-15"
  }
}
```

#### swot.analysis.completed

```json
{
  "eventId": "EVT345678",
  "eventType": "swot.analysis.completed",
  "timestamp": "2025-05-16T16:15:30Z",
  "resource": {
    "type": "student",
    "id": "STU12345"
  },
  "data": {
    "analysisId": "SWA98765",
    "strengthCount": 5,
    "weaknessCount": 3,
    "opportunityCount": 4,
    "threatCount": 2,
    "hasRecommendations": true,
    "analysisUrl": "/api/students/STU12345/swot/SWA98765"
  }
}
```

## Handling Webhooks

Your webhook endpoint should be designed to:

1. Verify the webhook signature
2. Acknowledge receipt quickly (respond with 2xx status)
3. Process events asynchronously (if processing is time-consuming)
4. Handle different event types appropriately

### Example Webhook Handler

```javascript
// Express.js webhook handler
const express = require('express');
const crypto = require('crypto');
const bodyParser = require('body-parser');

const app = express();

// It's important to use the raw body for signature verification
app.use(bodyParser.json({
  verify: (req, res, buf) => {
    req.rawBody = buf;
  }
}));

const WEBHOOK_SECRET = process.env.WEBHOOK_SECRET;

// Webhook handler
app.post('/api/webhooks/swot', (req, res) => {
  // 1. Verify signature
  const signature = req.headers['x-swot-signature'];
  const hmac = crypto.createHmac('sha256', WEBHOOK_SECRET);
  const digest = 'sha256=' + hmac.update(req.rawBody).digest('hex');
  
  if (signature !== digest) {
    return res.status(401).send('Invalid signature');
  }
  
  // 2. Send immediate acknowledgment
  res.status(200).send('Webhook received');
  
  // 3. Process asynchronously
  setImmediate(() => {
    processWebhook(req.body)
      .catch(error => {
        console.error(`Error processing webhook: ${error.message}`);
      });
  });
});

// Process different event types
async function processWebhook(event) {
  console.log(`Processing webhook event: ${event.eventId} (${event.eventType})`);
  
  switch (event.eventType) {
    case 'student.data.updated':
      await handleStudentUpdate(event);
      break;
      
    case 'academic.grade.added':
    case 'academic.grade.updated':
      await handleGradeEvent(event);
      break;
      
    case 'attendance.updated':
      await handleAttendanceUpdate(event);
      break;
      
    case 'behavior.incident.created':
    case 'behavior.incident.updated':
      await handleBehaviorEvent(event);
      break;
      
    case 'swot.analysis.completed':
    case 'swot.analysis.updated':
      await handleSWOTAnalysisEvent(event);
      break;
      
    default:
      console.log(`Unhandled event type: ${event.eventType}`);
  }
}

// Start the server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Webhook server listening on port ${PORT}`);
});
```

## Best Practices

### Implementation Best Practices

1. **Respond Quickly**: Return a 200 status code as quickly as possible, then process the webhook asynchronously
2. **Idempotent Processing**: Handle duplicate events gracefully (use event IDs to detect duplicates)
3. **Error Handling**: Implement comprehensive error handling and logging
4. **Queue Processing**: Use a queue system for webhook processing if you have high volume
5. **Monitor Webhook Health**: Track success rates, processing times, and error rates
6. **Implement Retry Logic**: Be prepared to handle temporary issues with your webhook processor

### Operational Best Practices

1. **Subscribe Selectively**: Only subscribe to events you need to reduce noise
2. **Test Thoroughly**: Use the webhook testing endpoint to verify your implementation
3. **Monitor Actively**: Set up alerts for webhook failures
4. **Document Dependencies**: Keep track of systems that depend on webhooks
5. **Maintain Redundancy**: Consider multiple webhook endpoints for critical systems

## Troubleshooting

### Common Issues

| Issue | Possible Causes | Solutions |
|-------|----------------|-----------|
| 401 response from platform | Invalid webhook signature | Verify secret, check signature calculation |
| Webhook not receiving events | Inactive webhook, incorrect URL | Check webhook status, test endpoint |
| Duplicate events | Retry mechanism, race condition | Implement idempotent processing |
| Delayed events | System load, network issues | Set appropriate timeouts, implement queuing |
| Missing events | Webhook filtering, service disruption | Check subscription settings, contact support |

### Testing Webhooks

Use the webhook test endpoint to simulate events:

```
POST /api/webhooks/test
```

Request:
```json
{
  "webhookId": "WHK78901",
  "eventType": "academic.grade.added",
  "testMode": true
}
```

This will send a test event to your registered webhook endpoint, allowing you to verify your implementation without waiting for actual events.

### Logging and Debugging

Implement comprehensive logging in your webhook handler:

1. Log all incoming webhook requests (excluding sensitive data)
2. Record signature verification results
3. Log processing start and completion
4. Track event types and processing times
5. Maintain error logs with detailed context

### Support Resources

If you encounter issues with webhooks:

- Check the webhook status in the platform dashboard
- Review webhook delivery logs in the platform
- Contact <EMAIL> for assistance

## Conclusion

Webhooks provide a powerful way to build real-time integrations with the SWOT Analysis Platform. By following the practices outlined in this guide, you can create robust, secure, and efficient webhook implementations that keep your systems in sync with the latest student data and analyses.

For additional integration examples, see the [API Integration Examples](/workspace/docs/api_integration_examples.md) document.
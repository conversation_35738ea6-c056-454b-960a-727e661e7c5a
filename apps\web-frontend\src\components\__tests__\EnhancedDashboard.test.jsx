import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import { ThemeProvider } from '@mui/material/styles';
import { I18nextProvider } from 'react-i18next';
import { BrowserRouter } from 'react-router-dom';

import EnhancedDashboard from '../Dashboard/EnhancedDashboard';
import theme from '../../theme';
import i18n from '../../i18n/config'; // Assume i18n config exists

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Test wrapper component
const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider theme={theme}>
      <I18nextProvider i18n={i18n}>
        {children}
      </I18nextProvider>
    </ThemeProvider>
  </BrowserRouter>
);

// Mock data
const mockMetrics = {
  totalStudents: { value: '1,247', change: '+12 this month', trend: 'up' },
  activeClasses: { value: '24', change: '+2 new classes', trend: 'up' },
  pendingReports: { value: '8', change: '-3 from last week', trend: 'down' },
  avgPerformance: { value: '78.5%', change: '+2.3% improvement', trend: 'up' },
};

const mockActivities = [
  {
    id: 1,
    title: 'New SWOT analysis completed for Class 10-A',
    time: '2 minutes ago',
    type: 'success',
    category: 'Analysis'
  },
  {
    id: 2,
    title: 'Parent meeting scheduled for tomorrow',
    time: '15 minutes ago',
    type: 'info',
    category: 'Meeting'
  },
];

// Mock hooks and modules
jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  useTranslation: () => ({
    t: (key) => key,
    i18n: { changeLanguage: jest.fn() },
  }),
}));

jest.mock('@mui/material/useMediaQuery', () => jest.fn(() => false));

describe('EnhancedDashboard', () => {
  beforeEach(() => {
    // Reset any mocks
    jest.clearAllMocks();
  });

  describe('Accessibility', () => {
    test('should not have accessibility violations', async () => {
      const { container } = render(
        <TestWrapper>
          <EnhancedDashboard />
        </TestWrapper>
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    test('should have proper heading structure', () => {
      render(
        <TestWrapper>
          <EnhancedDashboard />
        </TestWrapper>
      );

      // Check for main heading
      expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument();
      
      // Check for section headings
      const headings = screen.getAllByRole('heading');
      expect(headings.length).toBeGreaterThan(1);
    });

    test('should have proper ARIA labels for interactive elements', () => {
      render(
        <TestWrapper>
          <EnhancedDashboard />
        </TestWrapper>
      );

      // Check for buttons with accessible names
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toHaveAccessibleName();
      });
    });

    test('should support keyboard navigation', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <EnhancedDashboard />
        </TestWrapper>
      );

      // Tab through interactive elements
      await user.tab();
      expect(document.activeElement).toBeInTheDocument();
      
      // Should be able to activate buttons with Enter
      const firstButton = screen.getAllByRole('button')[0];
      firstButton.focus();
      await user.keyboard('{Enter}');
      
      // Should not throw errors
      expect(firstButton).toBeInTheDocument();
    });
  });

  describe('Responsive Design', () => {
    test('should render correctly on mobile', () => {
      // Mock mobile viewport
      jest.doMock('@mui/material/useMediaQuery', () => jest.fn(() => true));
      
      render(
        <TestWrapper>
          <EnhancedDashboard />
        </TestWrapper>
      );

      // Should render without errors
      expect(screen.getByText('dashboard:welcomeMessage')).toBeInTheDocument();
    });

    test('should adapt layout for different screen sizes', () => {
      const { rerender } = render(
        <TestWrapper>
          <EnhancedDashboard />
        </TestWrapper>
      );

      // Check initial render
      expect(screen.getByText('dashboard:welcomeMessage')).toBeInTheDocument();

      // Re-render with different viewport
      rerender(
        <TestWrapper>
          <EnhancedDashboard />
        </TestWrapper>
      );

      // Should still render correctly
      expect(screen.getByText('dashboard:welcomeMessage')).toBeInTheDocument();
    });
  });

  describe('Loading States', () => {
    test('should show loading skeletons initially', () => {
      render(
        <TestWrapper>
          <EnhancedDashboard />
        </TestWrapper>
      );

      // Should show loading indicators
      const loadingElements = screen.getAllByTestId(/skeleton/i);
      expect(loadingElements.length).toBeGreaterThan(0);
    });

    test('should hide loading state after data loads', async () => {
      render(
        <TestWrapper>
          <EnhancedDashboard />
        </TestWrapper>
      );

      // Wait for loading to complete
      await waitFor(() => {
        expect(screen.queryByTestId(/skeleton/i)).not.toBeInTheDocument();
      }, { timeout: 2000 });
    });
  });

  describe('Interactions', () => {
    test('should handle refresh button click', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <EnhancedDashboard />
        </TestWrapper>
      );

      // Wait for initial load
      await waitFor(() => {
        expect(screen.queryByTestId(/skeleton/i)).not.toBeInTheDocument();
      });

      // Find and click refresh button
      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      await user.click(refreshButton);

      // Should trigger loading state again
      expect(refreshButton).toBeDisabled();
    });

    test('should handle metric card clicks', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <EnhancedDashboard />
        </TestWrapper>
      );

      // Wait for data to load
      await waitFor(() => {
        expect(screen.queryByTestId(/skeleton/i)).not.toBeInTheDocument();
      });

      // Find metric cards and click them
      const metricCards = screen.getAllByTestId(/metric-card/i);
      if (metricCards.length > 0) {
        await user.click(metricCards[0]);
        // Should not throw errors
        expect(metricCards[0]).toBeInTheDocument();
      }
    });

    test('should handle quick action buttons', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <EnhancedDashboard />
        </TestWrapper>
      );

      // Find quick action buttons
      const addStudentButton = screen.getByText('dashboard:addStudent');
      await user.click(addStudentButton);

      // Should not throw errors
      expect(addStudentButton).toBeInTheDocument();
    });
  });

  describe('Data Display', () => {
    test('should display metrics correctly', async () => {
      render(
        <TestWrapper>
          <EnhancedDashboard />
        </TestWrapper>
      );

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('dashboard:totalStudents')).toBeInTheDocument();
      });

      // Check for metric titles
      expect(screen.getByText('dashboard:totalStudents')).toBeInTheDocument();
      expect(screen.getByText('dashboard:activeClasses')).toBeInTheDocument();
      expect(screen.getByText('dashboard:pendingReports')).toBeInTheDocument();
    });

    test('should display recent activities', async () => {
      render(
        <TestWrapper>
          <EnhancedDashboard />
        </TestWrapper>
      );

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('dashboard:recentActivity')).toBeInTheDocument();
      });

      // Should show activity section
      expect(screen.getByText('dashboard:recentActivity')).toBeInTheDocument();
    });

    test('should display performance overview', async () => {
      render(
        <TestWrapper>
          <EnhancedDashboard />
        </TestWrapper>
      );

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('dashboard:performanceOverview')).toBeInTheDocument();
      });

      // Should show performance section
      expect(screen.getByText('dashboard:performanceOverview')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    test('should handle API errors gracefully', async () => {
      // Mock console.error to avoid noise in tests
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <TestWrapper>
          <EnhancedDashboard />
        </TestWrapper>
      );

      // Should render without crashing even if data fails to load
      expect(screen.getByText('dashboard:welcomeMessage')).toBeInTheDocument();

      consoleSpy.mockRestore();
    });

    test('should show appropriate fallbacks for missing data', async () => {
      render(
        <TestWrapper>
          <EnhancedDashboard />
        </TestWrapper>
      );

      // Should handle missing data gracefully
      await waitFor(() => {
        expect(screen.getByText('dashboard:welcomeMessage')).toBeInTheDocument();
      });
    });
  });

  describe('Performance', () => {
    test('should render within acceptable time', async () => {
      const startTime = performance.now();
      
      render(
        <TestWrapper>
          <EnhancedDashboard />
        </TestWrapper>
      );

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Should render within 100ms (adjust threshold as needed)
      expect(renderTime).toBeLessThan(100);
    });

    test('should not cause memory leaks', () => {
      const { unmount } = render(
        <TestWrapper>
          <EnhancedDashboard />
        </TestWrapper>
      );

      // Should unmount without errors
      expect(() => unmount()).not.toThrow();
    });
  });

  describe('Internationalization', () => {
    test('should display translated content', () => {
      render(
        <TestWrapper>
          <EnhancedDashboard />
        </TestWrapper>
      );

      // Should use translation keys (mocked to return the key)
      expect(screen.getByText('dashboard:welcomeMessage')).toBeInTheDocument();
      expect(screen.getByText('dashboard:totalStudents')).toBeInTheDocument();
    });

    test('should handle language changes', async () => {
      const { rerender } = render(
        <TestWrapper>
          <EnhancedDashboard />
        </TestWrapper>
      );

      // Re-render with different language context
      rerender(
        <TestWrapper>
          <EnhancedDashboard />
        </TestWrapper>
      );

      // Should still render correctly
      expect(screen.getByText('dashboard:welcomeMessage')).toBeInTheDocument();
    });
  });
});

# Authentication and Authorization System Architecture

## Overview

This document details the architecture of the Authentication and Role-Based Access Control (RBAC) system for the SWOT Analysis Platform. The system provides secure user authentication, session management, and granular access control based on user roles and relationships.

![Auth System Architecture](https://i.imgur.com/XgOb8iZ.png)

## System Components

The authentication and authorization system consists of the following components:

### 1. Data Models

Core database models that store user, role, permission, and relationship information:

- **User Model**: Stores user account information and credentials
- **Role Model**: Defines user roles like admin, teacher, and parent
- **Permission Model**: Defines specific actions users can perform
- **Relationship Model**: Defines relationships between users and students
- **Session Model**: Manages active user sessions

### 2. Services

Business logic for authentication and authorization:

- **AuthService**: Provides high-level functions for login, logout, and access checks
- **SessionService**: Manages user sessions and session validation

### 3. Middleware

Components for integrating authentication with web applications:

- **AuthMiddleware**: Handles authentication checks for route handlers
- **PermissionMiddleware**: Validates user permissions for specific routes
- **RelationshipMiddleware**: Checks access to student data based on relationships

### 4. Interface

- **Command-line Interface**: For testing and administrative functions
- **API Definitions**: Endpoints for authentication and user management

## Data Model

### User

The User model represents user accounts in the system:

```sql
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    first_name TEXT,
    last_name TEXT,
    role_id TEXT NOT NULL,
    active BOOLEAN NOT NULL DEFAULT 1,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TEXT,
    last_login TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (role_id) REFERENCES roles(id)
);
```

### Role

The Role model defines user roles:

```sql
CREATE TABLE roles (
    id TEXT PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    is_default BOOLEAN DEFAULT 0,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL
);
```

### Permission

The Permission model defines specific permissions:

```sql
CREATE TABLE permissions (
    id TEXT PRIMARY KEY,
    code TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    module TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL
);
```

### Role-Permission Link

The role_permissions table links roles to permissions:

```sql
CREATE TABLE role_permissions (
    role_id TEXT NOT NULL,
    permission_id TEXT NOT NULL,
    created_at TEXT NOT NULL,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
);
```

### Relationship

The Relationship model defines relationships between users and students:

```sql
CREATE TABLE relationships (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    student_id TEXT NOT NULL,
    relation_type TEXT NOT NULL,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, student_id)
);
```

### Session

The Session model manages user sessions:

```sql
CREATE TABLE sessions (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    token TEXT UNIQUE NOT NULL,
    ip_address TEXT,
    user_agent TEXT,
    expires_at TEXT NOT NULL,
    last_activity TEXT NOT NULL,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

## Authentication Process

### Login Flow

1. User submits username/email and password
2. System verifies credentials against stored password hash
3. If valid, system creates a new session and generates a token
4. System increments failed login attempts for invalid password and may lock account
5. System returns session token to client for future requests

### Session Validation Flow

1. Client includes session token in requests
2. System validates token and checks if the session is active and not expired
3. System updates last activity timestamp for the session
4. If valid, system attaches user and permission data to the request
5. If invalid, system returns authentication error

### Logout Flow

1. Client submits token to logout endpoint
2. System invalidates the session by removing it
3. System confirms successful logout

## Authorization Process

### Role-Based Access Control (RBAC)

The system uses roles and permissions to control access:

1. **Roles**: Each user is assigned a role (admin, teacher, parent)
2. **Permissions**: Each role has a set of permissions
3. **Access Check**: System checks if the user's role has the required permission

Example role-permission mappings:

- **Admin Role**: All permissions
- **Teacher Role**: view_dashboard, view_students, view_student_details, edit_students
- **Parent Role**: view_dashboard, view_student_details

### Relationship-Based Access Control

For student data, the system uses relationships to control access:

1. **Relationships**: Define connections between users and students
2. **Relation Types**: Parent, Teacher, Admin
3. **Access Check**: System checks if the user has a relationship with the student

Access rules:
- **Admins**: Can access all student data
- **Teachers**: Can access data for students they are assigned to
- **Parents**: Can access data only for their children

## Security Features

### Password Security

1. **Hashing**: Passwords are hashed using bcrypt with a work factor of 12
2. **Salting**: Each password has a unique salt
3. **No Plain Text**: Passwords are never stored or transmitted in plain text

### Session Security

1. **Secure Tokens**: Session tokens are generated using cryptographically secure random functions
2. **Expiration**: Sessions automatically expire after a set time (default 24 hours)
3. **Activity Tracking**: Session activity is monitored and updated
4. **Secure Storage**: Session tokens should be stored securely by clients (e.g., in HTTP-only cookies)

### Account Protection

1. **Lockout**: Accounts are temporarily locked after multiple failed login attempts
2. **Password Reset**: Admin users can reset passwords for other users
3. **Activity Logging**: User activities are logged for audit purposes

## Role Definitions

### Admin Role

Administrators have full access to the system:

- Can manage users and roles
- Can access all student data
- Can perform all operations

### Teacher Role

Teachers have access to their assigned students:

- Can view the dashboard
- Can view and edit student details for assigned students
- Cannot manage users and roles

### Parent Role

Parents have limited access to their children's data:

- Can view the dashboard
- Can view student details for their children
- Cannot view other students
- Cannot edit student data
- Cannot manage users and roles

## Permission Definitions

The system defines the following permissions:

| Permission Code | Description |
|---------------|-------------|
| view_dashboard | Can view the main dashboard |
| view_students | Can view the student list |
| view_student_details | Can view detailed student information |
| edit_students | Can edit student information |
| manage_users | Can manage user accounts and roles |

## Relationship Types

The system defines the following relationship types:

| Relationship Type | Description |
|-----------------|-------------|
| parent | Parent-child relationship |
| teacher | Teacher-student relationship |
| admin | Administrative relationship |

## Integration with Web Frameworks

The authentication and authorization system can be integrated with different web frameworks:

### Flask Integration

```python
from flask import Flask, request, jsonify
from auth.middleware.auth_middleware import AuthMiddleware

app = Flask(__name__)

@app.route('/api/protected')
@AuthMiddleware.require_auth()
def protected_route():
    # This route requires authentication
    return jsonify({'message': 'Protected content'})

@app.route('/api/admin')
@AuthMiddleware.require_permission('manage_users')
def admin_route():
    # This route requires the manage_users permission
    return jsonify({'message': 'Admin content'})

@app.route('/api/student/<student_id>')
@AuthMiddleware.check_student_access()
def student_route(student_id):
    # This route checks if the user can access the student's data
    return jsonify({'message': f'Student {student_id} data'})
```

### FastAPI Integration

```python
from fastapi import FastAPI, Depends, HTTPException
from auth.services.auth_service import AuthService

app = FastAPI()

def validate_token(token: str):
    success, response = AuthService.validate_session(token)
    if not success:
        raise HTTPException(status_code=401, detail="Invalid token")
    return response['user']

def require_permission(permission_code: str):
    def dependency(user = Depends(validate_token)):
        if not AuthService.check_permission(user['id'], permission_code):
            raise HTTPException(status_code=403, detail="Permission denied")
        return user
    return dependency

@app.get("/api/protected")
def protected_route(user = Depends(validate_token)):
    return {"message": "Protected content"}

@app.get("/api/admin")
def admin_route(user = Depends(require_permission("manage_users"))):
    return {"message": "Admin content"}
```

## Usage Examples

### Authentication

```python
from auth.services.auth_service import AuthService

# Login
success, response = AuthService.login(
    username_or_email='<EMAIL>',
    password='adminpassword'
)

if success:
    user = response['user']
    session = response['session']
    token = session['token']
    print(f"Login successful for {user['username']}")
else:
    print(f"Login failed: {response['message']}")

# Validate session
success, response = AuthService.validate_session(token)

if success:
    user = response['user']
    permissions = response['permissions']
    print(f"Session valid for {user['username']}")
else:
    print(f"Session invalid: {response['message']}")

# Logout
success, response = AuthService.logout(token)

if success:
    print("Logout successful")
else:
    print(f"Logout failed: {response['message']}")
```

### Authorization

```python
from auth.services.auth_service import AuthService

# Check if user has a permission
has_permission = AuthService.check_permission(
    user_id='123',
    permission_code='view_student_details'
)

if has_permission:
    print("User can view student details")
else:
    print("User cannot view student details")

# Check if user can access a student's data
can_access = AuthService.can_access_student_data(
    user_id='123',
    student_id='STU12345'
)

if can_access:
    print("User can access this student's data")
else:
    print("User cannot access this student's data")

# Get all accessible students for a user
student_ids = AuthService.get_accessible_students(
    user_id='123'
)

print(f"User can access {len(student_ids)} students")
```

## Security Considerations

### Protecting Against Common Attacks

1. **SQL Injection**: Parameters are properly sanitized in database queries
2. **Cross-Site Scripting (XSS)**: Session tokens should be stored in HTTP-only cookies
3. **Cross-Site Request Forgery (CSRF)**: Implement CSRF tokens in forms
4. **Brute Force**: Account lockout after failed login attempts
5. **Session Hijacking**: Secure, expiring session tokens

### Data Privacy

1. **Sensitive Data**: Personal information is protected
2. **Data Access**: Data is only accessible to authorized users
3. **Audit Trails**: Actions are logged for accountability

## Future Enhancements

1. **Multi-factor Authentication (MFA)**: Add support for MFA
2. **OAuth/OpenID Integration**: Allow authentication through external providers
3. **Fine-grained Permissions**: Expand permission system for more detailed control
4. **Group-based Access Control**: Add support for user groups
5. **API Rate Limiting**: Prevent abuse with rate limiting
6. **Audit Logging**: Enhanced logging of security events

## Conclusion

The authentication and authorization system provides a secure, flexible foundation for the SWOT Analysis Platform. It ensures that users can only access data and features appropriate to their role, with special attention to student data privacy through relationship-based access control.
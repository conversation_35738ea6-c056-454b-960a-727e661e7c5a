# AI Service Implementation Guide
## Student SWOT Analysis Platform

This document provides a comprehensive guide for implementing the AI service integration with the Student SWOT Analysis Platform using Supabase Edge Functions and OpenAI.

## Table of Contents

1. [Introduction](#introduction)
2. [Architecture Overview](#architecture-overview)
3. [Prerequisites](#prerequisites)
4. [Implementation Steps](#implementation-steps)
5. [Deployment](#deployment)
6. [Testing](#testing)
7. [Monitoring and Maintenance](#monitoring-and-maintenance)
8. [Security Considerations](#security-considerations)
9. [Performance Optimization](#performance-optimization)
10. [Troubleshooting](#troubleshooting)

## Introduction

The AI service integration enhances the Student SWOT Analysis Platform with artificial intelligence capabilities to automatically generate SWOT analyses for students. This implementation leverages Supabase Edge Functions to securely process student data and generate insightful analyses using OpenAI's powerful language models.

### Key Features

- AI-powered SWOT analysis generation
- Secure API endpoint structure
- Rate limiting to prevent abuse
- Integration with Supabase authentication and database
- Comprehensive error handling

## Architecture Overview

The implementation follows a serverless architecture using Supabase Edge Functions:

```
┌─────────────┐      ┌──────────────┐      ┌─────────────┐
│             │      │              │      │             │
│ React       │ ───▶ │ Supabase     │ ───▶ │ OpenAI      │
│ Frontend    │ ◀─── │ Edge Function│ ◀─── │ API         │
│             │      │              │      │             │
└─────────────┘      └──────────────┘      └─────────────┘
       │                     │                    
       │                     │                    
       ▼                     ▼                    
┌─────────────┐      ┌──────────────┐           
│             │      │              │           
│ Supabase    │ ◀──▶ │ PostgreSQL   │           
│ Auth        │      │ Database     │           
│             │      │              │           
└─────────────┘      └──────────────┘           
```

### Components

1. **React Frontend**: Provides UI components for interacting with the AI service
2. **Supabase Edge Function**: Serverless function that processes requests and communicates with OpenAI
3. **OpenAI API**: Generates the SWOT analysis based on student data
4. **Supabase Auth**: Handles authentication and authorization
5. **PostgreSQL Database**: Stores student data and SWOT analyses

## Prerequisites

Before implementing the AI service, ensure you have:

1. A Supabase project set up with the following:
   - Authentication enabled
   - Database tables created according to the schema
   - Service role key for the Edge Function

2. An OpenAI API account with:
   - API key with sufficient credits
   - Access to required models

3. Development environment with:
   - Node.js 14+
   - Supabase CLI
   - Deno (for local Edge Function development)

## Implementation Steps

### 1. Set Up Database Tables and Functions

First, create the necessary database tables and functions for storing SWOT analyses and managing rate limiting:

1. **Create the SQL function for rate limiting**:

   Run the SQL script in `src/sql/rate_limiting_function.sql` in the Supabase SQL Editor.

### 2. Create the Supabase Edge Function

1. **Create the Edge Function structure**:

   ```bash
   mkdir -p supabase/functions/swot-analysis
   cd supabase/functions/swot-analysis
   ```

2. **Create the Edge Function files**:

   Create the following files in the `swot-analysis` directory:
   - `index.ts`: The main Edge Function code
   - `deno.json`: Deno configuration for local development
   - `import_map.json`: Deno import map for dependencies

3. **Implement the Edge Function**:

   The Edge Function should:
   - Accept student ID and academic term ID
   - Fetch student data from Supabase
   - Format the data for OpenAI
   - Call OpenAI to generate the SWOT analysis
   - Store the analysis in Supabase
   - Return the results

### 3. Implement Frontend Integration

1. **Create AI Service**:

   Implement the `aiService.js` file to handle communication with the Edge Function:
   - Generate SWOT analysis
   - Check rate limits
   - Increment usage counters

2. **Create React Hook**:

   Implement the `useAiAnalysis` hook to provide a simple interface for using the AI service:
   - Status management (loading, error, results)
   - Rate limit checking
   - Analysis generation
   - Error handling

3. **Create UI Component**:

   Implement the `AiSwotGenerator` component to provide a user interface for:
   - API key management
   - Student and term selection
   - Analysis generation
   - Result display
   - Error handling

### 4. Implement Error Handling

Implement comprehensive error handling in:

1. **Edge Function**: Proper error responses with status codes
2. **AI Service**: Error categorization and handling
3. **React Hook**: User-friendly error messages
4. **UI Component**: Error display and recovery options

### 5. Implement Rate Limiting

Set up rate limiting to prevent abuse:

1. **Database Function**: Tracks and enforces usage limits
2. **AI Service**: Checks limits before making API calls
3. **UI Component**: Displays usage information to users

## Deployment

### Deploy the Edge Function

1. **Deploy using the Supabase CLI**:

   ```bash
   supabase functions deploy swot-analysis --no-verify-jwt
   ```

2. **Set environment variables**:

   In the Supabase dashboard, set the following environment variables for the Edge Function:
   - `SUPABASE_URL`
   - `SUPABASE_SERVICE_ROLE_KEY`

### Deploy the Frontend Integration

1. **Install dependencies**:

   ```bash
   npm install @supabase/supabase-js openai
   ```

2. **Configure Supabase client**:

   Update the Supabase client configuration with your project URL and anon key.

3. **Integrate the components**:

   Add the AI components to your application:
   - Import and use the `AiSwotGenerator` component
   - Set up event handlers for analysis generation

## Testing

### Unit Testing

1. **Edge Function Tests**:

   - Test with various student data scenarios
   - Test error handling
   - Test rate limiting

2. **Frontend Tests**:

   - Test API interactions with mocked responses
   - Test UI component rendering
   - Test error handling

### Integration Testing

1. **End-to-End Testing**:

   - Test the complete flow from UI to OpenAI and back
   - Verify database operations
   - Test authentication and authorization

2. **Performance Testing**:

   - Test response times
   - Test concurrent requests
   - Test rate limiting effectiveness

## Monitoring and Maintenance

### Monitoring

1. **Edge Function Logs**:

   - Monitor function invocations
   - Track error rates
   - Analyze performance

2. **Database Monitoring**:

   - Track usage metrics
   - Monitor rate limit status

3. **OpenAI Usage**:

   - Monitor API calls
   - Track costs

### Maintenance

1. **Regular Updates**:

   - Keep dependencies up to date
   - Update OpenAI models as better ones become available

2. **Error Analysis**:

   - Regularly review error logs
   - Improve error handling

3. **Performance Optimization**:

   - Optimize database queries
   - Cache frequently accessed data

## Security Considerations

### API Key Management

1. **Client-Side API Keys**:

   - Never store OpenAI API keys on the server
   - Provide clear storage options for users
   - Implement secure transmission

2. **Service Role Key Protection**:

   - Store as environment variables
   - Never expose in client-side code
   - Implement least privilege access

### Authentication and Authorization

1. **JWT Validation**:

   - Validate JWT tokens for all requests
   - Ensure proper expiration and refresh

2. **Row Level Security**:

   - Implement RLS policies for all tables
   - Test security with different user roles

### Data Protection

1. **Data Minimization**:

   - Only send necessary data to OpenAI
   - Remove sensitive information

2. **Secure Transmission**:

   - Use HTTPS for all communications
   - Encrypt sensitive data

## Performance Optimization

### Edge Function Optimization

1. **Efficient Database Queries**:

   - Use proper indexes
   - Limit returned fields
   - Implement query caching

2. **Parallel Processing**:

   - Fetch data in parallel when possible
   - Process results concurrently

### OpenAI Optimization

1. **Prompt Engineering**:

   - Optimize prompts for better results
   - Balance detail with token usage

2. **Model Selection**:

   - Choose appropriate models for the task
   - Consider cost vs. quality tradeoffs

## Troubleshooting

### Common Issues

1. **Edge Function Errors**:

   - JWT validation failures
   - Environment variable issues
   - Database connection problems

2. **OpenAI Errors**:

   - API key issues
   - Rate limiting
   - Model availability

3. **Frontend Issues**:

   - CORS errors
   - Authentication problems
   - State management bugs

### Debugging Strategies

1. **Edge Function Debugging**:

   - Check function logs in Supabase dashboard
   - Test locally with Supabase CLI
   - Verify environment variables

2. **OpenAI Debugging**:

   - Test API keys directly
   - Check OpenAI status page
   - Verify request format

3. **Frontend Debugging**:

   - Use React DevTools
   - Check browser console for errors
   - Implement detailed logging
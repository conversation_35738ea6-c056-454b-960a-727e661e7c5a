import React, { useState } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  Paper,
  Avatar,
  Fade,
  useTheme,
  Alert,
  Snackbar,
} from '@mui/material';
import {
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationOnIcon,
  Schedule as ScheduleIcon,
  ArrowBack as ArrowBackIcon,
  Send as SendIcon,
  Support as SupportIcon,
  Business as BusinessIcon,
  School as SchoolIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const ContactPage = () => {
  const { t } = useTranslation('common');
  const navigate = useNavigate();
  const theme = useTheme();
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    organization: '',
    subject: '',
    message: '',
  });
  const [showSuccess, setShowSuccess] = useState(false);

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Simulate form submission
    console.log('Form submitted:', formData);
    setShowSuccess(true);
    setFormData({
      name: '',
      email: '',
      organization: '',
      subject: '',
      message: '',
    });
  };

  const contactInfo = [
    {
      icon: EmailIcon,
      title: 'Email Us',
      primary: '<EMAIL>',
      secondary: '<EMAIL>',
      color: theme.palette.primary.main,
    },
    {
      icon: PhoneIcon,
      title: 'Call Us',
      primary: '+91 98765 43210',
      secondary: '+91 98765 43211',
      color: theme.palette.secondary.main,
    },
    {
      icon: LocationOnIcon,
      title: 'Visit Us',
      primary: 'Mumbai, Maharashtra',
      secondary: 'India - 400001',
      color: theme.palette.success.main,
    },
    {
      icon: ScheduleIcon,
      title: 'Business Hours',
      primary: 'Mon - Fri: 9:00 AM - 6:00 PM',
      secondary: 'Sat: 10:00 AM - 4:00 PM',
      color: theme.palette.warning.main,
    },
  ];

  const supportOptions = [
    {
      icon: SupportIcon,
      title: 'Technical Support',
      description: 'Get help with platform usage, troubleshooting, and technical issues.',
      action: 'Get Support',
    },
    {
      icon: BusinessIcon,
      title: 'Sales Inquiry',
      description: 'Learn about pricing, features, and how VidyaMitra can benefit your institution.',
      action: 'Contact Sales',
    },
    {
      icon: SchoolIcon,
      title: 'Educational Partnership',
      description: 'Explore partnership opportunities and institutional collaborations.',
      action: 'Partner With Us',
    },
  ];

  return (
    <Box>
      {/* Header */}
      <Box sx={{ bgcolor: 'primary.main', color: 'white', py: 8 }}>
        <Container maxWidth="lg">
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/')}
            sx={{ color: 'white', mb: 4 }}
          >
            Back to Home
          </Button>
          <Typography variant="h2" sx={{ fontWeight: 700, mb: 3 }}>
            Contact Us
          </Typography>
          <Typography variant="h5" sx={{ opacity: 0.9, maxWidth: 800 }}>
            We're here to help you transform education. Get in touch with our team 
            for support, sales inquiries, or partnership opportunities.
          </Typography>
        </Container>
      </Box>

      {/* Contact Information */}
      <Box sx={{ py: 8, bgcolor: 'background.default' }}>
        <Container maxWidth="lg">
          <Typography variant="h3" sx={{ textAlign: 'center', fontWeight: 700, mb: 6 }}>
            Get In Touch
          </Typography>
          <Grid container spacing={4}>
            {contactInfo.map((info, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Fade in timeout={800 + index * 200}>
                  <Card
                    sx={{
                      height: '100%',
                      textAlign: 'center',
                      p: 3,
                      transition: 'all 0.3s ease-in-out',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: theme.shadows[8],
                      },
                    }}
                  >
                    <Avatar
                      sx={{
                        width: 64,
                        height: 64,
                        bgcolor: info.color,
                        mx: 'auto',
                        mb: 2,
                      }}
                    >
                      <info.icon sx={{ fontSize: 32 }} />
                    </Avatar>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                      {info.title}
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      {info.primary}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {info.secondary}
                    </Typography>
                  </Card>
                </Fade>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Contact Form and Support Options */}
      <Box sx={{ py: 8, bgcolor: 'background.paper' }}>
        <Container maxWidth="lg">
          <Grid container spacing={6}>
            {/* Contact Form */}
            <Grid item xs={12} md={8}>
              <Paper elevation={2} sx={{ p: 4 }}>
                <Typography variant="h4" sx={{ fontWeight: 600, mb: 3 }}>
                  Send us a Message
                </Typography>
                <Box component="form" onSubmit={handleSubmit}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Full Name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label="Email Address"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Organization/School"
                        name="organization"
                        value={formData.organization}
                        onChange={handleInputChange}
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Subject"
                        name="subject"
                        value={formData.subject}
                        onChange={handleInputChange}
                        required
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Message"
                        name="message"
                        multiline
                        rows={4}
                        value={formData.message}
                        onChange={handleInputChange}
                        required
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <Button
                        type="submit"
                        variant="contained"
                        size="large"
                        endIcon={<SendIcon />}
                        sx={{ px: 4, py: 1.5 }}
                      >
                        Send Message
                      </Button>
                    </Grid>
                  </Grid>
                </Box>
              </Paper>
            </Grid>

            {/* Support Options */}
            <Grid item xs={12} md={4}>
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 3 }}>
                How Can We Help?
              </Typography>
              {supportOptions.map((option, index) => (
                <Fade in timeout={800 + index * 200} key={index}>
                  <Card sx={{ mb: 3, p: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                      <Avatar
                        sx={{
                          width: 48,
                          height: 48,
                          bgcolor: 'primary.main',
                          mr: 2,
                        }}
                      >
                        <option.icon sx={{ fontSize: 24 }} />
                      </Avatar>
                      <Box>
                        <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                          {option.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {option.description}
                        </Typography>
                        <Button
                          variant="outlined"
                          size="small"
                          sx={{ textTransform: 'none' }}
                        >
                          {option.action}
                        </Button>
                      </Box>
                    </Box>
                  </Card>
                </Fade>
              ))}
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* FAQ Section */}
      <Box sx={{ py: 8, bgcolor: 'background.default' }}>
        <Container maxWidth="md">
          <Typography variant="h3" sx={{ textAlign: 'center', fontWeight: 700, mb: 6 }}>
            Frequently Asked Questions
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Paper elevation={1} sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  How does VidyaMitra ensure data privacy?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  We implement enterprise-grade security measures including data encryption, 
                  FERPA compliance, and strict access controls to protect student information.
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={12}>
              <Paper elevation={1} sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  Which educational boards are supported?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  VidyaMitra supports CBSE, ICSE, and all major State educational boards 
                  across India, with customizable features for each curriculum.
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={12}>
              <Paper elevation={1} sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  Is training provided for teachers and staff?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Yes, we provide comprehensive training sessions, documentation, and 
                  ongoing support to ensure successful platform adoption.
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Success Snackbar */}
      <Snackbar
        open={showSuccess}
        autoHideDuration={6000}
        onClose={() => setShowSuccess(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setShowSuccess(false)}
          severity="success"
          sx={{ width: '100%' }}
        >
          Thank you for your message! We'll get back to you within 24 hours.
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ContactPage;

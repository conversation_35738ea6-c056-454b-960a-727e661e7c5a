# SWOT Analysis Platform - Integration Examples

This document provides practical examples for integrating the SWOT Analysis Platform with various education systems. These code examples demonstrate common integration patterns to help you quickly implement your own integrations.

## Table of Contents

1. [School Information System (SIS) Integration](#school-information-system-sis-integration)
2. [Gradebook Integration](#gradebook-integration)
3. [Attendance System Integration](#attendance-system-integration)
4. [Learning Management System (LMS) Integration](#learning-management-system-lms-integration)
5. [Mobile Application Integration](#mobile-application-integration)
6. [Real-time Updates with Webhooks](#real-time-updates-with-webhooks)

## School Information System (SIS) Integration

### Student Data Synchronization

This example demonstrates how to synchronize student data from a SIS to the SWOT Analysis Platform.

```python
import requests
import json
import logging
from datetime import datetime

# Configuration
API_BASE_URL = "https://swot-platform.example.edu/api"
API_KEY = "your_api_key"
SIS_API_URL = "https://sis.school.edu/api"
SIS_API_KEY = "your_sis_api_key"

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='sis_sync.log'
)
logger = logging.getLogger('sis_sync')

def sync_students_from_sis():
    """Synchronize student data from SIS to SWOT platform"""
    
    logger.info("Starting student synchronization from SIS")
    
    try:
        # Get students from SIS
        sis_students = get_students_from_sis()
        
        if not sis_students:
            logger.warning("No students retrieved from SIS")
            return
            
        logger.info(f"Retrieved {len(sis_students)} students from SIS")
        
        # Format for SWOT platform
        swot_students = []
        for student in sis_students:
            swot_students.append({
                "studentId": student["id"],
                "firstName": student["first_name"],
                "lastName": student["last_name"],
                "grade": student["grade_level"],
                "classId": student["homeroom_id"],
                "email": student.get("email", ""),
                "guardians": [
                    {
                        "name": guardian["name"],
                        "relationship": guardian["relationship"],
                        "email": guardian.get("email", ""),
                        "phone": guardian.get("phone", "")
                    } for guardian in student.get("guardians", [])
                ]
            })
        
        # Import to SWOT platform
        headers = {
            "Authorization": f"Bearer {API_KEY}",
            "Content-Type": "application/json"
        }
        
        response = requests.post(
            f"{API_BASE_URL}/api/import/students",
            headers=headers,
            data=json.dumps({"students": swot_students})
        )
        
        if response.status_code == 200:
            result = response.json()
            logger.info(f"Successfully imported {result['imported']} students")
            return result['imported']
        else:
            logger.error(f"Error: {response.status_code} - {response.text}")
            return 0
            
    except Exception as e:
        logger.exception(f"Exception during student sync: {str(e)}")
        return 0

def get_students_from_sis():
    """Retrieve student data from the SIS API"""
    
    headers = {
        "Authorization": f"Bearer {SIS_API_KEY}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(
            f"{SIS_API_URL}/students",
            headers=headers,
            params={"include": "guardians,homeroom"}
        )
        
        if response.status_code == 200:
            return response.json()["data"]
        else:
            logger.error(f"SIS API error: {response.status_code} - {response.text}")
            return []
    except Exception as e:
        logger.exception(f"Exception retrieving students from SIS: {str(e)}")
        return []

if __name__ == "__main__":
    sync_students_from_sis()
```

### Scheduled Synchronization Script

This example shows how to set up a scheduled synchronization using a cron job:

```bash
#!/bin/bash
# sis_sync.sh - Run this script daily to synchronize student data

# Activate virtual environment if using one
source /path/to/your/venv/bin/activate

# Run the synchronization script
python /path/to/sync_students.py

# Log completion
echo "$(date) - SIS sync completed" >> /var/log/sis_sync.log

# Example crontab entry (run daily at 1 AM):
# 0 1 * * * /path/to/sis_sync.sh
```

## Gradebook Integration

### Grade Import Script

This example demonstrates how to import grades from a third-party gradebook system into the SWOT platform.

```python
import requests
import json
import pandas as pd
from datetime import datetime

# Configuration
GRADEBOOK_API_URL = "https://gradebook.example.edu/api"
GRADEBOOK_API_KEY = "your_gradebook_api_key"
SWOT_API_URL = "https://swot-platform.example.edu/api"
SWOT_API_KEY = "your_swot_api_key"

def sync_gradebook_scores():
    """Import academic data from gradebook to SWOT platform"""
    
    # Get current quarter info
    current_date = datetime.now()
    academic_year = f"{current_date.year}-{current_date.year + 1}" if current_date.month > 7 else f"{current_date.year - 1}-{current_date.year}"
    quarter = determine_current_quarter(current_date)
    
    print(f"Syncing grades for {academic_year} {quarter}")
    
    # Get grades from gradebook system
    headers_gradebook = {
        "Authorization": f"Bearer {GRADEBOOK_API_KEY}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(
        f"{GRADEBOOK_API_URL}/grades",
        headers=headers_gradebook,
        params={"quarter": quarter, "year": academic_year}
    )
    
    if response.status_code != 200:
        print(f"Error fetching grades: {response.status_code} - {response.text}")
        return
    
    gradebook_data = response.json()
    
    # Transform data for SWOT platform
    swot_academic_data = []
    for student in gradebook_data["students"]:
        student_grades = {
            "studentId": student["id"],
            "quarter": quarter,
            "academicYear": academic_year,
            "subjects": []
        }
        
        for subject, grades in student["subjects"].items():
            subject_data = {
                "name": subject,
                "score": calculate_average(grades["assignments"]),
                "grade": convert_to_letter_grade(calculate_average(grades["assignments"])),
                "assignments": []
            }
            
            for assignment in grades["assignments"]:
                subject_data["assignments"].append({
                    "name": assignment["name"],
                    "score": assignment["score"],
                    "maxScore": assignment["possible_points"],
                    "date": assignment["date"]
                })
            
            student_grades["subjects"].append(subject_data)
        
        swot_academic_data.append(student_grades)
    
    # Import to SWOT platform
    headers_swot = {
        "Authorization": f"Bearer {SWOT_API_KEY}",
        "Content-Type": "application/json"
    }
    
    response = requests.post(
        f"{SWOT_API_URL}/api/import/academic",
        headers=headers_swot,
        data=json.dumps({"students": swot_academic_data})
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"Successfully imported academic data for {result['imported']} students")
    else:
        print(f"Error: {response.status_code} - {response.text}")

def calculate_average(assignments):
    """Calculate weighted average for assignments"""
    total_points = sum(assignment["score"] for assignment in assignments)
    total_possible = sum(assignment["possible_points"] for assignment in assignments)
    return (total_points / total_possible) * 100 if total_possible > 0 else 0

def convert_to_letter_grade(score):
    """Convert numerical score to letter grade"""
    if score >= 90: return "A"
    elif score >= 80: return "B"
    elif score >= 70: return "C"
    elif score >= 60: return "D"
    else: return "F"

def determine_current_quarter(date):
    """Determine current academic quarter based on date"""
    month = date.month
    if month >= 8 and month <= 10: return "Q1"
    elif month >= 11 or month == 1: return "Q2"
    elif month >= 2 and month <= 4: return "Q3"
    else: return "Q4"

if __name__ == "__main__":
    sync_gradebook_scores()
```

## Attendance System Integration

This example demonstrates importing attendance data from an external attendance system into the SWOT Analysis Platform.

```python
import requests
import json
import pandas as pd
from datetime import datetime, timedelta

# Configuration
ATTENDANCE_API_URL = "https://attendance.example.edu/api"
ATTENDANCE_API_KEY = "your_attendance_api_key"
SWOT_API_URL = "https://swot-platform.example.edu/api"
SWOT_API_KEY = "your_swot_api_key"

def sync_attendance_data():
    """Synchronize attendance data from external system to SWOT platform"""
    
    # Calculate date range for current quarter
    current_date = datetime.now()
    start_date, end_date = get_quarter_date_range(current_date)
    
    print(f"Syncing attendance data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    
    # Get attendance data from attendance system
    headers_attendance = {
        "Authorization": f"Bearer {ATTENDANCE_API_KEY}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(
        f"{ATTENDANCE_API_URL}/attendance",
        headers=headers_attendance,
        params={
            "start_date": start_date.strftime("%Y-%m-%d"),
            "end_date": end_date.strftime("%Y-%m-%d")
        }
    )
    
    if response.status_code != 200:
        print(f"Error fetching attendance data: {response.status_code} - {response.text}")
        return
    
    attendance_data = response.json()
    
    # Transform data for SWOT platform
    swot_attendance_data = []
    for student in attendance_data["students"]:
        student_attendance = {
            "studentId": student["id"],
            "academicYear": f"{current_date.year}-{current_date.year + 1}" if current_date.month > 7 else f"{current_date.year - 1}-{current_date.year}",
            "quarter": get_current_quarter(current_date),
            "records": []
        }
        
        for date, status in student["attendance"].items():
            student_attendance["records"].append({
                "date": date,
                "status": map_attendance_status(status),
                "isExcused": status.get("excused", False),
                "notes": status.get("notes", "")
            })
        
        swot_attendance_data.append(student_attendance)
    
    # Import to SWOT platform
    headers_swot = {
        "Authorization": f"Bearer {SWOT_API_KEY}",
        "Content-Type": "application/json"
    }
    
    response = requests.post(
        f"{SWOT_API_URL}/api/import/attendance",
        headers=headers_swot,
        data=json.dumps({"students": swot_attendance_data})
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"Successfully imported attendance data for {result['imported']} students")
    else:
        print(f"Error: {response.status_code} - {response.text}")

def map_attendance_status(status):
    """Map external attendance codes to SWOT platform codes"""
    status_code = status.get("code", "").lower()
    if status_code in ["p", "present"]:
        return "present"
    elif status_code in ["t", "tardy"]:
        return "tardy"
    elif status_code in ["a", "absent"]:
        return "absent"
    else:
        return "unknown"

def get_current_quarter(date):
    """Determine current academic quarter based on date"""
    month = date.month
    if month >= 8 and month <= 10: return "Q1"
    elif month >= 11 or month == 1: return "Q2"
    elif month >= 2 and month <= 4: return "Q3"
    else: return "Q4"

def get_quarter_date_range(date):
    """Get start and end dates for current quarter"""
    quarter = get_current_quarter(date)
    year = date.year
    
    if quarter == "Q1":
        return datetime(year, 8, 1), datetime(year, 10, 31)
    elif quarter == "Q2":
        if date.month == 1:
            return datetime(year-1, 11, 1), datetime(year, 1, 31)
        else:
            return datetime(year, 11, 1), datetime(year+1, 1, 31)
    elif quarter == "Q3":
        return datetime(year, 2, 1), datetime(year, 4, 30)
    else:  # Q4
        return datetime(year, 5, 1), datetime(year, 7, 31)

if __name__ == "__main__":
    sync_attendance_data()
```

## Learning Management System (LMS) Integration

This example demonstrates how to integrate the SWOT Analysis Platform with a Learning Management System to display SWOT analyses within the LMS interface.

```javascript
// Node.js with Express.js example
const express = require('express');
const axios = require('axios');
const router = express.Router();

// Configuration
const SWOT_API_URL = 'https://swot-platform.example.edu/api';
const SWOT_API_KEY = process.env.SWOT_API_KEY; // Store in environment variable

// LMS middleware - add this to your existing LMS plugin
router.get('/lms/student/:studentId/swot', async (req, res) => {
  try {
    // Verify LMS user has permission to view this student's data
    if (!hasPermission(req.user, req.params.studentId)) {
      return res.status(403).json({ error: 'Permission denied' });
    }
    
    // Get SWOT analysis from SWOT platform
    const response = await axios.get(
      `${SWOT_API_URL}/api/students/${req.params.studentId}/swot/latest`,
      {
        headers: {
          'Authorization': `Bearer ${SWOT_API_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    // Format for LMS display
    const swotData = response.data;
    const lmsViewData = {
      studentId: swotData.studentId,
      studentName: swotData.studentName,
      generatedDate: new Date(swotData.generatedAt).toLocaleDateString(),
      swotQuadrants: {
        strengths: swotData.strengths,
        weaknesses: swotData.weaknesses,
        opportunities: swotData.opportunities,
        threats: swotData.threats
      },
      recommendations: swotData.recommendations || [],
      visualizationUrls: swotData.visualizations || {}
    };
    
    // For API request, return JSON
    if (req.headers.accept === 'application/json') {
      return res.json(lmsViewData);
    }
    
    // For web request, render LMS template
    return res.render('lms/swot_analysis', { 
      title: 'Student SWOT Analysis',
      data: lmsViewData
    });
    
  } catch (error) {
    console.error(`Error fetching SWOT data: ${error.message}`);
    
    return res.status(500).json({ 
      error: 'Failed to retrieve SWOT analysis',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Utility function to check permissions
function hasPermission(user, studentId) {
  // Sample permission logic - customize for your LMS:
  // Teachers can view their students
  // Admins can view all students
  // Parents can view only their children
  if (user.role === 'admin') return true;
  if (user.role === 'teacher' && user.assignedStudents.includes(studentId)) return true;
  if (user.role === 'parent' && user.children.includes(studentId)) return true;
  return false;
}

module.exports = router;
```

### LMS Widget Integration

This example shows how to create a widget for displaying SWOT data within an LMS dashboard:

```html
<!-- LMS Widget Template (EJS, Handlebars, or your template system) -->
<div class="swot-widget card">
  <div class="card-header">
    <h4>SWOT Analysis for <%= data.studentName %></h4>
    <div class="subtitle">Generated on <%= data.generatedDate %></div>
  </div>
  
  <div class="card-body">
    <div class="swot-grid">
      <!-- Strengths -->
      <div class="swot-quadrant strengths">
        <h5>Strengths</h5>
        <ul>
          <% data.swotQuadrants.strengths.forEach(function(item) { %>
            <li><%= item.description %></li>
          <% }); %>
        </ul>
      </div>
      
      <!-- Weaknesses -->
      <div class="swot-quadrant weaknesses">
        <h5>Weaknesses</h5>
        <ul>
          <% data.swotQuadrants.weaknesses.forEach(function(item) { %>
            <li><%= item.description %></li>
          <% }); %>
        </ul>
      </div>
      
      <!-- Opportunities -->
      <div class="swot-quadrant opportunities">
        <h5>Opportunities</h5>
        <ul>
          <% data.swotQuadrants.opportunities.forEach(function(item) { %>
            <li><%= item.description %></li>
          <% }); %>
        </ul>
      </div>
      
      <!-- Threats -->
      <div class="swot-quadrant threats">
        <h5>Threats</h5>
        <ul>
          <% data.swotQuadrants.threats.forEach(function(item) { %>
            <li><%= item.description %></li>
          <% }); %>
        </ul>
      </div>
    </div>
    
    <% if (data.visualizationUrls.quadrant) { %>
      <div class="swot-visualization">
        <img src="<%= data.visualizationUrls.quadrant %>" 
             alt="SWOT Visualization" 
             class="img-fluid" />
      </div>
    <% } %>
    
    <% if (data.recommendations && data.recommendations.length > 0) { %>
      <div class="recommendations-section">
        <h5>Recommendations</h5>
        <ul>
          <% data.recommendations.forEach(function(rec) { %>
            <li class="priority-<%= rec.priority %>"><%= rec.text %></li>
          <% }); %>
        </ul>
      </div>
    <% } %>
    
    <div class="widget-footer">
      <a href="/lms/student/<%= data.studentId %>/swot/full" class="btn btn-primary">
        View Full Analysis
      </a>
    </div>
  </div>
</div>

<style>
  .swot-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 15px;
    margin-bottom: 20px;
  }
  
  .swot-quadrant {
    padding: 10px;
    border-radius: 5px;
  }
  
  .strengths { background-color: #d4edda; }
  .weaknesses { background-color: #fff3cd; }
  .opportunities { background-color: #cce5ff; }
  .threats { background-color: #f8d7da; }
  
  .priority-high { color: #dc3545; }
  .priority-medium { color: #fd7e14; }
  .priority-low { color: #28a745; }
</style>
```

## Mobile Application Integration

This example demonstrates how to integrate the SWOT Analysis Platform with a mobile application using Swift for iOS.

```swift
// Swift example for iOS app
import Foundation
import UIKit

// SWOT API Client
class SWOTApiClient {
    private let baseUrl = "https://swot-platform.example.edu/api"
    private let apiKey: String
    private let session: URLSession
    
    init(apiKey: String) {
        self.apiKey = apiKey
        
        // Configure session with reasonable timeouts
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30.0
        config.timeoutIntervalForResource = 60.0
        self.session = URLSession(configuration: config)
    }
    
    // Fetch SWOT Analysis for a student
    func fetchStudentSWOT(studentId: String, completion: @escaping (Result<SWOTAnalysis, Error>) -> Void) {
        guard let url = URL(string: "\(baseUrl)/api/students/\(studentId)/swot/latest") else {
            completion(.failure(NSError(domain: "SWOTApiClient", code: 1, userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])))
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.addValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let task = session.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse,
                  (200...299).contains(httpResponse.statusCode) else {
                let statusCode = (response as? HTTPURLResponse)?.statusCode ?? 0
                completion(.failure(NSError(domain: "SWOTApiClient", code: statusCode, userInfo: [NSLocalizedDescriptionKey: "Server returned error status \(statusCode)"])))
                return
            }
            
            guard let data = data else {
                completion(.failure(NSError(domain: "SWOTApiClient", code: 3, userInfo: [NSLocalizedDescriptionKey: "No data received"])))
                return
            }
            
            do {
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                let swotAnalysis = try decoder.decode(SWOTAnalysis.self, from: data)
                completion(.success(swotAnalysis))
            } catch {
                completion(.failure(error))
            }
        }
        
        task.resume()
    }
    
    // Fetch visualization image
    func fetchVisualization(url: URL, completion: @escaping (Result<UIImage, Error>) -> Void) {
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.addValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        
        let task = session.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse,
                  (200...299).contains(httpResponse.statusCode) else {
                let statusCode = (response as? HTTPURLResponse)?.statusCode ?? 0
                completion(.failure(NSError(domain: "SWOTApiClient", code: statusCode, userInfo: [NSLocalizedDescriptionKey: "Server returned error status \(statusCode)"])))
                return
            }
            
            guard let data = data, let image = UIImage(data: data) else {
                completion(.failure(NSError(domain: "SWOTApiClient", code: 4, userInfo: [NSLocalizedDescriptionKey: "Invalid image data"])))
                return
            }
            
            completion(.success(image))
        }
        
        task.resume()
    }
}

// Model definitions
struct SWOTAnalysis: Decodable {
    let studentId: String
    let studentName: String
    let generatedAt: Date
    let strengths: [SWOTItem]
    let weaknesses: [SWOTItem]
    let opportunities: [SWOTItem]
    let threats: [SWOTItem]
    let recommendations: [Recommendation]?
    let visualizations: VisualizationURLs?
}

struct SWOTItem: Decodable {
    let id: String
    let description: String
    let category: String
    let value: Double?
    let urgency: String?
}

struct Recommendation: Decodable {
    let id: String
    let text: String
    let type: String
    let priority: String
}

struct VisualizationURLs: Decodable {
    let quadrant: String?
    let academicTrend: String?
    let attendanceTrend: String?
    let behaviorTrend: String?
}

// View Controller Example
class StudentSWOTViewController: UIViewController {
    
    @IBOutlet weak var studentNameLabel: UILabel!
    @IBOutlet weak var generatedDateLabel: UILabel!
    @IBOutlet weak var strengthsTextView: UITextView!
    @IBOutlet weak var weaknessesTextView: UITextView!
    @IBOutlet weak var opportunitiesTextView: UITextView!
    @IBOutlet weak var threatsTextView: UITextView!
    @IBOutlet weak var quadrantImageView: UIImageView!
    @IBOutlet weak var loadingIndicator: UIActivityIndicatorView!
    
    let studentId: String
    let apiClient: SWOTApiClient
    
    init(studentId: String, apiClient: SWOTApiClient) {
        self.studentId = studentId
        self.apiClient = apiClient
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        loadingIndicator.startAnimating()
        loadSWOTAnalysis()
    }
    
    private func loadSWOTAnalysis() {
        apiClient.fetchStudentSWOT(studentId: studentId) { [weak self] result in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                self.loadingIndicator.stopAnimating()
                
                switch result {
                case .success(let swot):
                    self.updateUI(with: swot)
                case .failure(let error):
                    self.showError(error)
                }
            }
        }
    }
    
    private func updateUI(with swot: SWOTAnalysis) {
        // Update basic info
        studentNameLabel.text = swot.studentName
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .medium
        generatedDateLabel.text = "Generated on \(dateFormatter.string(from: swot.generatedAt))"
        
        // Update SWOT text
        strengthsTextView.text = swot.strengths.map { "• \($0.description)" }.joined(separator: "\n")
        weaknessesTextView.text = swot.weaknesses.map { "• \($0.description)" }.joined(separator: "\n")
        opportunitiesTextView.text = swot.opportunities.map { "• \($0.description)" }.joined(separator: "\n")
        threatsTextView.text = swot.threats.map { "• \($0.description)" }.joined(separator: "\n")
        
        // Load visualization if available
        if let quadrantUrlString = swot.visualizations?.quadrant,
           let quadrantUrl = URL(string: quadrantUrlString) {
            
            apiClient.fetchVisualization(url: quadrantUrl) { [weak self] result in
                guard let self = self else { return }
                
                DispatchQueue.main.async {
                    switch result {
                    case .success(let image):
                        self.quadrantImageView.image = image
                    case .failure(let error):
                        print("Failed to load visualization: \(error.localizedDescription)")
                    }
                }
            }
        }
    }
    
    private func showError(_ error: Error) {
        let alert = UIAlertController(
            title: "Error Loading Data",
            message: "Failed to load SWOT analysis: \(error.localizedDescription)",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "Retry", style: .default) { [weak self] _ in
            guard let self = self else { return }
            self.loadingIndicator.startAnimating()
            self.loadSWOTAnalysis()
        })
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        
        present(alert, animated: true)
    }
}

// Usage example in app delegate or coordinator
let client = SWOTApiClient(apiKey: "your_api_key")
let studentSWOTViewController = StudentSWOTViewController(studentId: "STU12345", apiClient: client)
navigationController.pushViewController(studentSWOTViewController, animated: true)
```

## Real-time Updates with Webhooks

This example shows how to implement a webhook handler to receive real-time updates from the SWOT Analysis Platform.

```javascript
// Node.js webhook handler with Express
const express = require('express');
const crypto = require('crypto');
const bodyParser = require('body-parser');

const app = express();
app.use(bodyParser.json());

const WEBHOOK_SECRET = process.env.WEBHOOK_SECRET; // Store in environment variable

// Webhook verification middleware
function verifyWebhookSignature(req, res, next) {
  const signature = req.headers['x-swot-signature'];
  if (!signature) {
    return res.status(401).send('No signature provided');
  }
  
  const payload = JSON.stringify(req.body);
  const hmac = crypto.createHmac('sha256', WEBHOOK_SECRET);
  const digest = 'sha256=' + hmac.update(payload).digest('hex');
  
  if (!crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(digest))) {
    return res.status(401).send('Invalid signature');
  }
  
  next();
}

// Webhook endpoint
app.post('/api/swot-webhooks', verifyWebhookSignature, async (req, res) => {
  try {
    const event = req.body;
    
    // Log webhook receipt (for debugging and audit trail)
    console.log(`Received webhook event: ${event.eventId} - ${event.eventType}`);
    
    // Send immediate acknowledgment to prevent retries
    res.status(200).send('Webhook received');
    
    // Process different event types
    switch (event.eventType) {
      case 'student.data.updated':
        await handleStudentDataUpdated(event);
        break;
        
      case 'academic.grade.added':
        await handleGradeAdded(event);
        break;
        
      case 'swot.analysis.completed':
        await handleSWOTAnalysisCompleted(event);
        break;
        
      case 'behavior.incident.created':
        await handleBehaviorIncident(event);
        break;
        
      default:
        console.log(`Unhandled event type: ${event.eventType}`);
    }
    
  } catch (error) {
    // Since we already sent a 200 response, log the error but don't send to client
    console.error(`Error processing webhook: ${error.message}`);
  }
});

// Event handlers
async function handleStudentDataUpdated(event) {
  const studentId = event.resource.id;
  console.log(`Student data updated for ${studentId}`);
  
  // Example: Update local cache or database
  await updateStudentRecord(studentId, event.data);
  
  // Example: Notify relevant staff
  if (event.data.significantChange) {
    await notifyStudentAdvisor(studentId, 'Student data has been significantly updated');
  }
}

async function handleGradeAdded(event) {
  const studentId = event.resource.id;
  const gradeData = event.data;
  
  console.log(`New grade for ${studentId}: ${gradeData.subject} - ${gradeData.score}`);
  
  // Example: Update local gradebook
  await updateLocalGradebook(studentId, gradeData);
  
  // Example: Send alerts for low grades
  if (gradeData.score < 70) {
    await sendLowGradeAlert(studentId, gradeData);
  }
}

async function handleSWOTAnalysisCompleted(event) {
  const studentId = event.resource.id;
  console.log(`SWOT analysis completed for ${studentId}`);
  
  // Example: Update dashboard
  await updateDashboard(studentId);
  
  // Example: Notify teacher if there are high-priority threats
  if (event.data.threatCount > 0) {
    await notifyTeacher(studentId, `SWOT analysis identified ${event.data.threatCount} threats`);
  }
}

async function handleBehaviorIncident(event) {
  const studentId = event.resource.id;
  const incidentData = event.data;
  
  console.log(`Behavior incident for ${studentId}: ${incidentData.type}`);
  
  // Example: Update behavior tracking system
  await updateBehaviorSystem(studentId, incidentData);
  
  // Example: Escalate serious incidents
  if (incidentData.severity === 'high') {
    await escalateIncident(studentId, incidentData);
  }
}

// Implementation of handler functions would go here

// Start the server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Webhook server listening on port ${PORT}`);
});
```

## Conclusion

These integration examples provide a starting point for connecting the SWOT Analysis Platform with various education systems. When implementing your own integrations, remember to:

1. **Handle errors gracefully** - Implement proper error handling and retries
2. **Validate data** - Ensure data formats match the API specifications
3. **Secure credentials** - Store API keys and secrets securely (environment variables, secure vaults)
4. **Log activity** - Maintain logs for debugging and audit purposes
5. **Implement rate limiting** - Respect API rate limits to avoid throttling

For additional assistance or custom integration support, contact the SWOT Platform API support <NAME_EMAIL>.
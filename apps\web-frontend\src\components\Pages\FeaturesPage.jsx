import React from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Avatar,
  Chip,
  Button,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Fade,
  useTheme,
} from '@mui/material';
import {
  Assessment as AssessmentIcon,
  School as SchoolIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  Language as LanguageIcon,
  Security as SecurityIcon,
  CloudSync as CloudSyncIcon,
  Analytics as AnalyticsIcon,
  ArrowBack as ArrowBackIcon,
  CheckCircle as CheckCircleIcon,
  Psychology as PsychologyIcon,
  Dashboard as DashboardIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const FeaturesPage = () => {
  const { t } = useTranslation('common');
  const navigate = useNavigate();
  const theme = useTheme();

  const coreFeatures = [
    {
      icon: AssessmentIcon,
      title: 'AI-Powered SWOT Analysis',
      description: 'Advanced artificial intelligence algorithms analyze student data to generate comprehensive SWOT reports.',
      features: [
        'Automated strength identification',
        'Weakness pattern recognition',
        'Opportunity mapping',
        'Threat assessment',
        'Personalized recommendations'
      ],
      color: theme.palette.primary.main,
    },
    {
      icon: SchoolIcon,
      title: 'Multi-Board Support',
      description: 'Complete support for CBSE, ICSE, and all major State educational boards across India.',
      features: [
        'CBSE curriculum alignment',
        'ICSE standards compliance',
        'State board customization',
        'Regional language support',
        'Board-specific analytics'
      ],
      color: theme.palette.secondary.main,
    },
    {
      icon: TrendingUpIcon,
      title: 'Performance Tracking',
      description: 'Real-time monitoring and analysis of student academic and behavioral performance.',
      features: [
        'Grade tracking',
        'Attendance monitoring',
        'Behavioral analysis',
        'Progress visualization',
        'Trend identification'
      ],
      color: theme.palette.success.main,
    },
    {
      icon: PeopleIcon,
      title: 'Multi-Stakeholder Platform',
      description: 'Dedicated interfaces for teachers, parents, administrators, and students.',
      features: [
        'Teacher dashboard',
        'Parent portal',
        'Admin analytics',
        'Student interface',
        'Role-based permissions'
      ],
      color: theme.palette.warning.main,
    },
  ];

  const additionalFeatures = [
    {
      icon: LanguageIcon,
      title: 'Multi-Language Support',
      description: 'Interface available in English, Hindi, and major regional languages.',
    },
    {
      icon: SecurityIcon,
      title: 'Data Security',
      description: 'Enterprise-grade security with FERPA compliance and data encryption.',
    },
    {
      icon: CloudSyncIcon,
      title: 'Cloud Integration',
      description: 'Seamless cloud synchronization with offline capabilities.',
    },
    {
      icon: AnalyticsIcon,
      title: 'Advanced Analytics',
      description: 'Comprehensive reporting with exportable insights and visualizations.',
    },
    {
      icon: PsychologyIcon,
      title: 'Behavioral Insights',
      description: 'AI-driven analysis of student behavior patterns and social interactions.',
    },
    {
      icon: DashboardIcon,
      title: 'Intuitive Dashboards',
      description: 'User-friendly interfaces designed specifically for Indian educational context.',
    },
  ];

  return (
    <Box>
      {/* Header */}
      <Box sx={{ bgcolor: 'primary.main', color: 'white', py: 8 }}>
        <Container maxWidth="lg">
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/')}
            sx={{ color: 'white', mb: 4 }}
          >
            Back to Home
          </Button>
          <Typography variant="h2" sx={{ fontWeight: 700, mb: 3 }}>
            Platform Features
          </Typography>
          <Typography variant="h5" sx={{ opacity: 0.9, maxWidth: 800 }}>
            Discover the comprehensive suite of features designed specifically for 
            Indian educational institutions and stakeholders.
          </Typography>
        </Container>
      </Box>

      {/* Core Features */}
      <Box sx={{ py: 8, bgcolor: 'background.default' }}>
        <Container maxWidth="lg">
          <Typography variant="h3" sx={{ textAlign: 'center', fontWeight: 700, mb: 6 }}>
            Core Features
          </Typography>
          <Grid container spacing={4}>
            {coreFeatures.map((feature, index) => (
              <Grid item xs={12} md={6} key={index}>
                <Fade in timeout={800 + index * 200}>
                  <Card
                    sx={{
                      height: '100%',
                      p: 3,
                      transition: 'all 0.3s ease-in-out',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: theme.shadows[8],
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 3 }}>
                      <Avatar
                        sx={{
                          width: 56,
                          height: 56,
                          bgcolor: feature.color,
                          mr: 2,
                        }}
                      >
                        <feature.icon sx={{ fontSize: 28 }} />
                      </Avatar>
                      <Box>
                        <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
                          {feature.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {feature.description}
                        </Typography>
                      </Box>
                    </Box>
                    <List dense>
                      {feature.features.map((item, itemIndex) => (
                        <ListItem key={itemIndex} sx={{ px: 0 }}>
                          <ListItemIcon sx={{ minWidth: 32 }}>
                            <CheckCircleIcon sx={{ fontSize: 20, color: feature.color }} />
                          </ListItemIcon>
                          <ListItemText 
                            primary={item}
                            primaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Card>
                </Fade>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Additional Features */}
      <Box sx={{ py: 8, bgcolor: 'background.paper' }}>
        <Container maxWidth="lg">
          <Typography variant="h3" sx={{ textAlign: 'center', fontWeight: 700, mb: 6 }}>
            Additional Capabilities
          </Typography>
          <Grid container spacing={4}>
            {additionalFeatures.map((feature, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Fade in timeout={800 + index * 150}>
                  <Card
                    sx={{
                      height: '100%',
                      textAlign: 'center',
                      p: 3,
                      transition: 'all 0.3s ease-in-out',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: theme.shadows[6],
                      },
                    }}
                  >
                    <Avatar
                      sx={{
                        width: 64,
                        height: 64,
                        bgcolor: 'primary.main',
                        mx: 'auto',
                        mb: 2,
                      }}
                    >
                      <feature.icon sx={{ fontSize: 32 }} />
                    </Avatar>
                    <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                      {feature.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {feature.description}
                    </Typography>
                  </Card>
                </Fade>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Indian Education Focus */}
      <Box sx={{ py: 8, bgcolor: 'secondary.main', color: 'white' }}>
        <Container maxWidth="lg">
          <Grid container spacing={6} alignItems="center">
            <Grid item xs={12} md={6}>
              <Typography variant="h3" sx={{ fontWeight: 700, mb: 3 }}>
                Built for Indian Education
              </Typography>
              <Typography variant="h6" sx={{ mb: 4, opacity: 0.9 }}>
                Every feature is designed with the Indian educational context in mind, 
                ensuring cultural sensitivity and local relevance.
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Chip
                  label="CBSE Aligned"
                  sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
                />
                <Chip
                  label="ICSE Compatible"
                  sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
                />
                <Chip
                  label="State Board Ready"
                  sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
                />
                <Chip
                  label="Multi-Language"
                  sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
                />
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Paper elevation={4} sx={{ p: 4 }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                  Supported Educational Boards
                </Typography>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText primary="Central Board of Secondary Education (CBSE)" />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText primary="Indian Certificate of Secondary Education (ICSE)" />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText primary="State Education Boards (All Major States)" />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText primary="International Baccalaureate (IB)" />
                  </ListItem>
                </List>
              </Paper>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Call to Action */}
      <Box sx={{ py: 8, bgcolor: 'background.default', textAlign: 'center' }}>
        <Container maxWidth="md">
          <Typography variant="h3" sx={{ fontWeight: 700, mb: 3 }}>
            Experience the Power of VidyaMitra
          </Typography>
          <Typography variant="h6" color="text.secondary" sx={{ mb: 4 }}>
            Start your journey towards data-driven education today.
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              onClick={() => navigate('/login')}
              sx={{ px: 4, py: 1.5 }}
            >
              Try VidyaMitra
            </Button>
            <Button
              variant="outlined"
              size="large"
              onClick={() => navigate('/contact')}
              sx={{ px: 4, py: 1.5 }}
            >
              Request Demo
            </Button>
          </Box>
        </Container>
      </Box>
    </Box>
  );
};

export default FeaturesPage;

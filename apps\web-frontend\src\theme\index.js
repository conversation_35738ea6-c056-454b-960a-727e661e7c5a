import { createTheme } from '@mui/material/styles';

// VidyaMitra Design System Theme
// Optimized for Indian educational context with cultural sensitivity and accessibility

const baseTheme = {
  // Color palette based on Indian cultural preferences
  palette: {
    mode: 'light',
    primary: {
      50: '#E3F2FD',
      100: '#BBDEFB',
      200: '#90CAF9',
      300: '#64B5F6',
      400: '#42A5F5',
      500: '#2E5BA8', // Main brand color - Knowledge & Trust
      600: '#1E88E5',
      700: '#1976D2',
      800: '#1565C0',
      900: '#0D47A1',
      main: '#2E5BA8',
      light: '#64B5F6',
      dark: '#1976D2',
      contrastText: '#ffffff',
    },
    secondary: {
      50: '#FFF3E0',
      100: '#FFE0B2',
      200: '#FFCC80',
      300: '#FFB74D',
      400: '#FFA726',
      500: '#FF9933', // Saffron - Cultural significance
      600: '#FB8C00',
      700: '#F57C00',
      800: '#EF6C00',
      900: '#E65100',
      main: '#FF9933',
      light: '#FFB74D',
      dark: '#F57C00',
      contrastText: '#ffffff',
    },
    success: {
      50: '#E8F5E8',
      100: '#C8E6C9',
      200: '#A5D6A7',
      300: '#81C784',
      400: '#66BB6A',
      500: '#138808', // Indian flag green
      600: '#43A047',
      700: '#388E3C',
      800: '#2E7D32',
      900: '#1B5E20',
      main: '#138808',
      light: '#81C784',
      dark: '#388E3C',
      contrastText: '#ffffff',
    },
    warning: {
      50: '#FFFBF0',
      100: '#FFF4E0',
      200: '#FFECB3',
      300: '#FFE082',
      400: '#FFD54F',
      500: '#F4C430', // Mustard yellow
      600: '#FFB300',
      700: '#F9A825',
      800: '#F57F17',
      900: '#FF6F00',
      main: '#F4C430',
      light: '#FFD54F',
      dark: '#F9A825',
      contrastText: '#000000',
    },
    error: {
      50: '#FFEBEE',
      100: '#FFCDD2',
      200: '#EF9A9A',
      300: '#E57373',
      400: '#EF5350',
      500: '#D81159', // Vermilion
      600: '#E53935',
      700: '#D32F2F',
      800: '#C62828',
      900: '#B71C1C',
      main: '#D81159',
      light: '#EF5350',
      dark: '#D32F2F',
      contrastText: '#ffffff',
    },
    grey: {
      50: '#FAFAFA',
      100: '#F5F5F5',
      200: '#EEEEEE',
      300: '#E0E0E0',
      400: '#BDBDBD',
      500: '#9E9E9E',
      600: '#757575',
      700: '#616161',
      800: '#424242',
      900: '#212121',
    },
    background: {
      default: '#FAFAFA',
      paper: '#FFFFFF',
      elevated: '#FFFFFF',
    },
    text: {
      primary: '#212121',
      secondary: '#616161',
      disabled: '#9E9E9E',
    },
    divider: '#E0E0E0',
    action: {
      active: '#2E5BA8',
      hover: 'rgba(46, 91, 168, 0.04)',
      selected: 'rgba(46, 91, 168, 0.08)',
      disabled: 'rgba(0, 0, 0, 0.26)',
      disabledBackground: 'rgba(0, 0, 0, 0.12)',
    },
  },

  // Typography system with Indic script support
  typography: {
    fontFamily: [
      'Noto Sans',
      'Roboto',
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Arial',
      'sans-serif',
      '"Apple Color Emoji"',
      '"Segoe UI Emoji"',
      '"Segoe UI Symbol"',
    ].join(','),
    fontFamilyDevanagari: [
      'Noto Sans Devanagari',
      'Hind',
      'Mangal',
      'sans-serif',
    ].join(','),
    fontFamilyBengali: [
      'Noto Sans Bengali',
      'Hind Siliguri',
      'SolaimanLipi',
      'sans-serif',
    ].join(','),
    fontFamilyTamil: [
      'Noto Sans Tamil',
      'Catamaran',
      'Latha',
      'sans-serif',
    ].join(','),
    fontFamilyTelugu: [
      'Noto Sans Telugu',
      'Mallanna',
      'Gautami',
      'sans-serif',
    ].join(','),
    fontFamilyGujarati: [
      'Noto Sans Gujarati',
      'Hind Vadodara',
      'Shruti',
      'sans-serif',
    ].join(','),
    fontFamilyMono: [
      'JetBrains Mono',
      'Fira Code',
      'Consolas',
      'monospace',
    ].join(','),
    
    // Responsive font sizes
    h1: {
      fontSize: 'clamp(2.25rem, 1.9rem + 1.75vw, 3rem)',
      fontWeight: 700,
      lineHeight: 1.25,
      letterSpacing: '-0.025em',
    },
    h2: {
      fontSize: 'clamp(1.875rem, 1.6rem + 1.375vw, 2.25rem)',
      fontWeight: 600,
      lineHeight: 1.3,
      letterSpacing: '-0.025em',
    },
    h3: {
      fontSize: 'clamp(1.5rem, 1.3rem + 1vw, 1.875rem)',
      fontWeight: 600,
      lineHeight: 1.35,
    },
    h4: {
      fontSize: 'clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem)',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    h5: {
      fontSize: 'clamp(1.125rem, 1rem + 0.625vw, 1.25rem)',
      fontWeight: 600,
      lineHeight: 1.45,
    },
    h6: {
      fontSize: 'clamp(1rem, 0.9rem + 0.5vw, 1.125rem)',
      fontWeight: 600,
      lineHeight: 1.5,
    },
    body1: {
      fontSize: 'clamp(1rem, 0.9rem + 0.5vw, 1.125rem)',
      lineHeight: 1.6,
    },
    body2: {
      fontSize: 'clamp(0.875rem, 0.8rem + 0.375vw, 1rem)',
      lineHeight: 1.5,
    },
    caption: {
      fontSize: 'clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem)',
      lineHeight: 1.4,
    },
    button: {
      fontSize: 'clamp(0.875rem, 0.8rem + 0.375vw, 1rem)',
      fontWeight: 500,
      textTransform: 'none',
      letterSpacing: '0.025em',
    },
  },

  // Spacing system based on 4px grid
  spacing: 4,

  // Shape and border radius
  shape: {
    borderRadius: 8,
  },

  // Breakpoints optimized for Indian device usage
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 900,
      lg: 1200,
      xl: 1536,
    },
  },

  // Z-index scale
  zIndex: {
    mobileStepper: 1000,
    fab: 1050,
    speedDial: 1050,
    appBar: 1100,
    drawer: 1200,
    modal: 1300,
    snackbar: 1400,
    tooltip: 1500,
  },

  // Transitions
  transitions: {
    easing: {
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
      easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',
      bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    },
    duration: {
      shortest: 150,
      shorter: 200,
      short: 250,
      standard: 300,
      complex: 375,
      enteringScreen: 225,
      leavingScreen: 195,
    },
  },
};

// Create the theme with component customizations
const theme = createTheme({
  ...baseTheme,
  components: {
    // Global styles
    MuiCssBaseline: {
      styleOverrides: {
        html: {
          fontSize: '16px',
          scrollBehavior: 'smooth',
        },
        body: {
          backgroundColor: baseTheme.palette.background.default,
          fontFamily: baseTheme.typography.fontFamily,
          lineHeight: 1.6,
          '-webkit-font-smoothing': 'antialiased',
          '-moz-osx-font-smoothing': 'grayscale',
        },
        '*': {
          boxSizing: 'border-box',
        },
        '*:focus-visible': {
          outline: `2px solid ${baseTheme.palette.primary.main}`,
          outlineOffset: '2px',
        },
      },
    },

    // AppBar customization
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: '#FFFFFF',
          color: baseTheme.palette.text.primary,
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)',
          borderBottom: `1px solid ${baseTheme.palette.divider}`,
        },
      },
    },

    // Button customizations
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 500,
          minHeight: 44, // Touch-friendly
          padding: '12px 24px',
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            transform: 'translateY(-1px)',
          },
        },
        contained: {
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
          '&:hover': {
            boxShadow: '0 4px 8px rgba(0, 0, 0, 0.15)',
          },
        },
      },
    },

    // Card customizations
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)',
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06)',
            transform: 'translateY(-1px)',
          },
        },
      },
    },

    // Drawer customizations
    MuiDrawer: {
      styleOverrides: {
        paper: {
          backgroundColor: baseTheme.palette.primary.main,
          color: '#FFFFFF',
          borderRight: 'none',
        },
      },
    },

    // List item customizations for navigation
    MuiListItem: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          margin: '4px 8px',
          '&:hover': {
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
          },
          '&.Mui-selected': {
            backgroundColor: 'rgba(255, 255, 255, 0.15)',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
            },
          },
        },
      },
    },

    // TextField customizations
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 8,
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: baseTheme.palette.primary.main,
            },
          },
        },
      },
    },

    // Chip customizations
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          fontWeight: 500,
        },
      },
    },

    // Paper customizations
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 8,
        },
        elevation1: {
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)',
        },
        elevation2: {
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06)',
        },
        elevation3: {
          boxShadow: '0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05)',
        },
      },
    },

    // Tooltip customizations
    MuiTooltip: {
      styleOverrides: {
        tooltip: {
          backgroundColor: baseTheme.palette.grey[800],
          fontSize: '0.875rem',
          borderRadius: 6,
          padding: '8px 12px',
        },
      },
    },
  },
});

export default theme;

# AI Service API Documentation
## Student SWOT Analysis Platform

This document outlines the API endpoints, request/response formats, and usage patterns for the AI service integration with the Student SWOT Analysis Platform.

## Overview

The AI Service provides AI-powered SWOT analysis generation for students by analyzing their academic performance, attendance, behavior, and extracurricular activities.

## Base URL

For Supabase Edge Functions:
```
https://[YOUR-SUPABASE-PROJECT-ID].supabase.co/functions/v1/
```

## Authentication

All API endpoints require authentication. The following authentication methods are supported:

1. **Supabase Authentication**: JWT tokens obtained through Supabase Auth are used for most operations.
2. **API Key Authentication**: Required for OpenAI API access (client-provided).

## Rate Limiting

All AI operations are subject to rate limiting to prevent abuse and manage costs:

| Plan | Daily Limit | Monthly Limit |
|------|-------------|---------------|
| Free | 5 | 50 |
| Standard | 20 | 200 |
| Premium | 50 | 500 |
| Unlimited | 999 | 9999 |

The limits reset daily at midnight UTC and monthly on the first day of each month.

## Endpoints

### Generate SWOT Analysis

Generates a comprehensive SWOT analysis for a student using AI.

**URL**: `/swot-analysis`

**Method**: `POST`

**Auth required**: Yes (Supabase JWT)

**Request Body**:
```json
{
  "studentId": "student-uuid",
  "academicTermId": "term-uuid",
  "apiKey": "openai-api-key"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "swot-analysis-uuid",
    "studentId": "student-uuid",
    "academicTermId": "term-uuid",
    "generatedOn": "2025-05-18T10:35:06Z",
    "items": {
      "strengths": [
        {
          "id": "strength-item-uuid",
          "description": "Excellent mathematical abilities",
          "priority": "High",
          "category": "Academic",
          "source": "ai_generated"
        },
        ...
      ],
      "weaknesses": [...],
      "opportunities": [...],
      "threats": [...]
    },
    "recommendations": [
      {
        "id": "recommendation-uuid",
        "description": "Enroll in advanced mathematics courses",
        "priority": "High",
        "category": "Academic",
        "action_items": ["Speak with math department", "Register for next term's advanced course"]
      },
      ...
    ]
  }
}
```

### Check Rate Limit Status

Checks the current rate limit status for the authenticated user.

**Method**: Custom RPC function called from client

**Auth required**: Yes (Supabase JWT)

**Response**:
```json
{
  "dailyUsage": 3,
  "monthlyUsage": 25,
  "dailyLimit": 20,
  "monthlyLimit": 200,
  "canGenerate": true
}
```

### Increment Usage Counter

Increments the usage counter for the authenticated user.

**Method**: Custom RPC function called from client

**Auth required**: Yes (Supabase JWT)

**Response**:
```json
{
  "dailyUsage": 4,
  "monthlyUsage": 26,
  "dailyLimit": 20,
  "monthlyLimit": 200,
  "canGenerate": true
}
```

## Error Handling

The API returns standard HTTP status codes along with JSON error messages:

| Status Code | Description |
|-------------|-------------|
| 400 | Bad Request - Invalid parameters |
| 401 | Unauthorized - Authentication required |
| 403 | Forbidden - Rate limit exceeded or insufficient permissions |
| 404 | Not Found - Resource not found |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Server Error - Something went wrong on the server |

**Error Response Format**:
```json
{
  "error": "Error message",
  "details": "Additional error details (optional)"
}
```

## Client Integration

### Frontend Implementation

The Student SWOT Analysis Platform provides React components and hooks for easy integration with the AI service:

1. **AiSwotGenerator Component**: UI component for generating SWOT analysis
2. **useAiAnalysis Hook**: React hook for using the AI service

### Example Usage

```jsx
import { useAiAnalysis } from '../hooks/useAiAnalysis';
import AiSwotGenerator from '../components/AiSwotGenerator';

function StudentAnalysisPage({ studentId, academicTermId }) {
  const { generateAnalysis, loading, error, swotAnalysis } = useAiAnalysis();
  
  const handleAnalysisGenerated = (analysis) => {
    console.log('SWOT analysis generated:', analysis);
    // Update UI with the analysis
  };
  
  return (
    <div>
      <AiSwotGenerator
        studentId={studentId}
        academicTermId={academicTermId}
        onAnalysisGenerated={handleAnalysisGenerated}
      />
      
      {/* Rest of your component */}
    </div>
  );
}
```

## Security Considerations

1. **API Key Protection**: OpenAI API keys are never stored on the server and should be provided by the client for each request.
2. **JWT Validation**: All requests to the Edge Functions are validated using Supabase JWT.
3. **Data Isolation**: Row Level Security ensures users can only access data they are authorized to see.
4. **Rate Limiting**: Prevents abuse and excessive usage.

## Implementation Notes

1. **Edge Functions**: The AI analysis is performed using Supabase Edge Functions to optimize response times.
2. **Supabase Integration**: The solution integrates with Supabase for authentication, database operations, and serverless functions.
3. **OpenAI Integration**: The service uses OpenAI API for generating the SWOT analysis.
4. **Error Handling**: Comprehensive error handling is implemented throughout the service.

## Deployment

### Edge Function Deployment

To deploy the Edge Function:

```bash
cd edge-functions/swot-analysis
supabase functions deploy swot-analysis --no-verify-jwt
```

### Configuration

Set the following environment variables in your Supabase project:

1. `OPENAI_API_KEY` (if using a server-side API key)
2. `SUPABASE_URL`
3. `SUPABASE_SERVICE_ROLE_KEY`

### Rate Limit Database Setup

Execute the SQL function in the Supabase SQL Editor:

```sql
-- Create AI rate limits table and functions
-- See rate_limiting_function.sql for details
```